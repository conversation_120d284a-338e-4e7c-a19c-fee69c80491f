# 权限设置说明

## 问题描述

在 Windows 上编译的 macOS/Linux 可执行文件默认没有执行权限，会导致以下错误：

```bash
sudo: ./augment-cleaner-darwin-amd64-v2.1: command not found
```

## 解决方案

### 方法一：使用权限设置脚本（推荐）

1. 将整个项目文件夹复制到 macOS/Linux 系统
2. 在终端中进入项目目录
3. 运行权限设置脚本：

```bash
chmod +x set-permissions.sh
./set-permissions.sh
```

### 方法二：手动设置权限

为特定文件添加执行权限：

```bash
# 为 macOS AMD64 版本添加权限
chmod +x dist/augment-cleaner-darwin-amd64-v*

# 为 macOS ARM64 版本添加权限  
chmod +x dist/augment-cleaner-darwin-arm64-v*

# 为 Linux AMD64 版本添加权限
chmod +x dist/augment-cleaner-linux-amd64-v*

# 为 Linux ARM64 版本添加权限
chmod +x dist/augment-cleaner-linux-arm64-v*

# 为 Linux ARM32 版本添加权限
chmod +x dist/augment-cleaner-linux-arm-v*
```

### 方法三：批量设置权限

为所有非 Windows 可执行文件添加权限：

```bash
chmod +x dist/augment-cleaner-*
```

注意：这会尝试为所有文件添加权限，包括 .exe 文件，但不会造成问题。

## 验证权限设置

设置权限后，可以通过以下命令验证：

```bash
ls -la dist/
```

正确的权限应该显示为：`-rwxr-xr-x`（可执行文件）

## 运行程序

权限设置完成后，就可以正常运行程序了：

```bash
# macOS AMD64
./dist/augment-cleaner-darwin-amd64-v2.1

# macOS ARM64 (Apple Silicon)
./dist/augment-cleaner-darwin-arm64-v2.1

# Linux AMD64
./dist/augment-cleaner-linux-amd64-v2.1
```

## 预防措施

为了避免这个问题，建议：

1. 在对应的操作系统上进行编译
2. 或者在构建脚本中自动设置权限（仅适用于 Unix 系统）
3. 在分发时提供权限设置说明
