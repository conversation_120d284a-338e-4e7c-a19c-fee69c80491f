#!/bin/bash

# Augment清理工具 - 权限设置脚本
# 用于在 macOS/Linux 上为可执行文件添加执行权限

echo "🔐 Augment清理工具 - 权限设置"
echo "=============================="

# 检查dist目录是否存在
if [ ! -d "dist" ]; then
    echo "❌ 未找到 dist 目录"
    echo "请先运行构建脚本生成可执行文件"
    exit 1
fi

echo "📁 检查可执行文件..."

# 为所有非Windows可执行文件添加执行权限
count=0
for file in dist/augment-cleaner-*; do
    # 跳过Windows exe文件
    if [[ "$file" == *.exe ]]; then
        continue
    fi
    
    # 检查文件是否存在
    if [ -f "$file" ]; then
        echo "设置权限: $(basename "$file")"
        chmod +x "$file"
        count=$((count + 1))
    fi
done

if [ $count -eq 0 ]; then
    echo "⚠️  未找到需要设置权限的文件"
else
    echo "✅ 已为 $count 个文件设置执行权限"
fi

echo ""
echo "📋 当前文件权限:"
ls -la dist/ | grep -v ".exe"

echo ""
echo "🎉 权限设置完成！"
echo ""
echo "现在可以直接运行对应平台的可执行文件："
echo "  ./dist/augment-cleaner-linux-amd64-v*"
echo "  ./dist/augment-cleaner-darwin-amd64-v*"
echo "  等等..."
