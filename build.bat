@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

rem Augment清理工具构建脚本 - 批处理版本

echo 🧹 Augment清理工具 - 跨平台构建
echo ==================================

rem 版本管理
if not exist "version.txt" (
    echo 2.0 > version.txt
    echo 📝 创建初始版本文件: 2.0
)

rem 读取当前版本
set /p CURRENT_VERSION=<version.txt
echo 📋 当前版本: %CURRENT_VERSION%

rem 计算新版本 (增加0.1)
for /f "tokens=1,2 delims=." %%a in ("%CURRENT_VERSION%") do (
    set MAJOR=%%a
    set MINOR=%%b
)
set /a NEW_MINOR=%MINOR%+1
set NEW_VERSION=%MAJOR%.%NEW_MINOR%

echo 🔄 更新版本: %CURRENT_VERSION% → %NEW_VERSION%
echo %NEW_VERSION% > version.txt

rem 检查Go环境
where go >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Go编译环境
    echo 请先安装Go: https://golang.org/dl/
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('go version') do set GO_VERSION=%%i
echo ✅ 检测到Go编译环境: !GO_VERSION!

rem 创建构建目录
if not exist "dist" mkdir dist

echo 📦 开始编译清理工具 v%NEW_VERSION%...

echo.
echo 🏗️ AMD64架构 (x86_64)
rem 构建Windows AMD64版本
echo 构建 windows/amd64...
set GOOS=windows
set GOARCH=amd64
go build -o "dist\augment-cleaner-windows-amd64-v%NEW_VERSION%.exe" augment_cleaner.go
if %errorlevel% equ 0 (
    echo ✅ augment-cleaner-windows-amd64-v%NEW_VERSION%.exe
) else (
    echo ❌ 构建失败: augment-cleaner-windows-amd64-v%NEW_VERSION%.exe
)

rem 构建Linux AMD64版本
echo 构建 linux/amd64...
set GOOS=linux
set GOARCH=amd64
go build -o "dist\augment-cleaner-linux-amd64-v%NEW_VERSION%" augment_cleaner.go
if %errorlevel% equ 0 (
    echo ✅ augment-cleaner-linux-amd64-v%NEW_VERSION%
) else (
    echo ❌ 构建失败: augment-cleaner-linux-amd64-v%NEW_VERSION%
)

rem 构建macOS AMD64版本
echo 构建 darwin/amd64...
set GOOS=darwin
set GOARCH=amd64
go build -o "dist\augment-cleaner-darwin-amd64-v%NEW_VERSION%" augment_cleaner.go
if %errorlevel% equ 0 (
    echo ✅ augment-cleaner-darwin-amd64-v%NEW_VERSION%
) else (
    echo ❌ 构建失败: augment-cleaner-darwin-amd64-v%NEW_VERSION%
)

echo.
echo 🏗️ ARM64架构 (现代ARM设备)
rem 构建Windows ARM64版本
echo 构建 windows/arm64...
set GOOS=windows
set GOARCH=arm64
go build -o "dist\augment-cleaner-windows-arm64-v%NEW_VERSION%.exe" augment_cleaner.go
if %errorlevel% equ 0 (
    echo ✅ augment-cleaner-windows-arm64-v%NEW_VERSION%.exe
) else (
    echo ❌ 构建失败: augment-cleaner-windows-arm64-v%NEW_VERSION%.exe
)

rem 构建Linux ARM64版本
echo 构建 linux/arm64...
set GOOS=linux
set GOARCH=arm64
go build -o "dist\augment-cleaner-linux-arm64-v%NEW_VERSION%" augment_cleaner.go
if %errorlevel% equ 0 (
    echo ✅ augment-cleaner-linux-arm64-v%NEW_VERSION%
) else (
    echo ❌ 构建失败: augment-cleaner-linux-arm64-v%NEW_VERSION%
)

rem 构建macOS ARM64版本
echo 构建 darwin/arm64...
set GOOS=darwin
set GOARCH=arm64
go build -o "dist\augment-cleaner-darwin-arm64-v%NEW_VERSION%" augment_cleaner.go
if %errorlevel% equ 0 (
    echo ✅ augment-cleaner-darwin-arm64-v%NEW_VERSION%
) else (
    echo ❌ 构建失败: augment-cleaner-darwin-arm64-v%NEW_VERSION%
)

echo.
echo 🏗️ ARM32架构 (嵌入式设备)
rem 构建Linux ARM32版本
echo 构建 linux/arm...
set GOOS=linux
set GOARCH=arm
go build -o "dist\augment-cleaner-linux-arm-v%NEW_VERSION%" augment_cleaner.go
if %errorlevel% equ 0 (
    echo ✅ augment-cleaner-linux-arm-v%NEW_VERSION%
) else (
    echo ❌ 构建失败: augment-cleaner-linux-arm-v%NEW_VERSION%
)

echo.
echo 📁 构建结果:
dir /B dist\

echo.
echo 🎉 构建完成！版本: v%NEW_VERSION%
echo.
echo 使用方法:
echo.
echo AMD64架构 (x86_64):
echo   Windows: dist\augment-cleaner-windows-amd64-v%NEW_VERSION%.exe
echo   Linux:   dist\augment-cleaner-linux-amd64-v%NEW_VERSION%
echo   macOS:   dist\augment-cleaner-darwin-amd64-v%NEW_VERSION%
echo.
echo ARM64架构 (Apple Silicon, ARM服务器):
echo   Windows: dist\augment-cleaner-windows-arm64-v%NEW_VERSION%.exe
echo   Linux:   dist\augment-cleaner-linux-arm64-v%NEW_VERSION%
echo   macOS:   dist\augment-cleaner-darwin-arm64-v%NEW_VERSION%
echo.
echo ARM32架构 (树莓派等嵌入式设备):
echo   Linux:   dist\augment-cleaner-linux-arm-v%NEW_VERSION%
echo.
echo 提示: 可以直接双击exe文件运行Windows版本
echo 📝 版本已更新至: v%NEW_VERSION%
echo.
pause 