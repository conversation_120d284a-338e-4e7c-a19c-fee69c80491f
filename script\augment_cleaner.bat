@echo off
REM Augment插件清理工具 v2.0 - Windows版本
REM 支持全系列JetBrains产品的Augment插件数据清理

setlocal enabledelayedexpansion

REM 版本信息
set VERSION=2.0
set SCRIPT_NAME=Augment插件清理工具

REM 配置变量
set DRY_RUN=false
set VERBOSE=false
set SAFE_MODE=true
set CREATE_BACKUP=true
set SILENT_MODE=false
set SELECTED_IDE=
set CHAT_ONLY=false
set KEEP_SETTINGS=false

REM 统计变量
set FILES_DELETED=0
set DIRS_DELETED=0
set CONFIGS_CLEANED=0
set CACHES_CLEARED=0
set PROJECTS_CLEANED=0
set BACKUP_CREATED=false
set BACKUP_PATH=

REM JetBrains产品列表
set JETBRAINS_PRODUCTS=IntelliJIdea PyCharm WebStorm PhpStorm RubyMine CLion DataGrip GoLand Rider AndroidStudio

REM 颜色代码（Windows 10+支持）
for /f %%A in ('echo prompt $E ^| cmd') do set "ESC=%%A"
set RED=%ESC%[31m
set GREEN=%ESC%[32m
set YELLOW=%ESC%[33m
set BLUE=%ESC%[34m
set PURPLE=%ESC%[35m
set CYAN=%ESC%[36m
set NC=%ESC%[0m

REM 解析命令行参数
:parse_args
if "%~1"=="" goto :args_done
if "%~1"=="-h" goto :show_help
if "%~1"=="--help" goto :show_help
if "%~1"=="-v" set VERBOSE=true & shift & goto :parse_args
if "%~1"=="--verbose" set VERBOSE=true & shift & goto :parse_args
if "%~1"=="-d" set DRY_RUN=true & shift & goto :parse_args
if "%~1"=="--dry-run" set DRY_RUN=true & shift & goto :parse_args
if "%~1"=="-s" set SILENT_MODE=true & shift & goto :parse_args
if "%~1"=="--silent" set SILENT_MODE=true & shift & goto :parse_args
if "%~1"=="--no-backup" set CREATE_BACKUP=false & shift & goto :parse_args
if "%~1"=="--unsafe" set SAFE_MODE=false & shift & goto :parse_args
if "%~1"=="--chat-only" set CHAT_ONLY=true & shift & goto :parse_args
if "%~1"=="--keep-settings" set KEEP_SETTINGS=true & shift & goto :parse_args
if "%~1" NEQ "" (
    if "%~1:~0,6%"=="--ide=" (
        set SELECTED_IDE=%~1:~6%
        shift
        goto :parse_args
    )
)
echo 未知选项: %~1
echo 使用 -h 或 --help 查看帮助信息
exit /b 1

:args_done

REM 日志函数
:log
if "%SILENT_MODE%"=="false" (
    echo %BLUE%[%time:~0,8%]%NC% %~1
)
goto :eof

:verbose
if "%VERBOSE%"=="true" if "%SILENT_MODE%"=="false" (
    echo   %CYAN%→%NC% %~1
)
goto :eof

:warn
if "%SILENT_MODE%"=="false" (
    echo %YELLOW%⚠️  %~1%NC%
)
goto :eof

:error
echo %RED%❌ %~1%NC%
goto :eof

:success
if "%SILENT_MODE%"=="false" (
    echo %GREEN%✅ %~1%NC%
)
goto :eof

:info
if "%SILENT_MODE%"=="false" (
    echo %PURPLE%ℹ️  %~1%NC%
)
goto :eof

REM 显示帮助信息
:show_help
echo %SCRIPT_NAME% v%VERSION%
echo 支持全系列JetBrains产品的Augment插件数据清理
echo.
echo 用法:
echo   %~nx0 [选项]
echo.
echo 选项:
echo   -h, --help          显示帮助信息
echo   -v, --verbose       详细输出模式
echo   -d, --dry-run       干运行模式（只显示将要清理的文件）
echo   -s, --silent        静默模式（无交互）
echo   --no-backup         禁用备份
echo   --unsafe            非安全模式（更彻底的清理）
echo   --ide=NAME          只清理指定的IDE（如：IntelliJIdea）
echo   --chat-only         只清理聊天记录
echo   --keep-settings     保留用户设置
echo.
echo 支持的IDE:
echo   - IntelliJ IDEA (IntelliJIdea)
echo   - PyCharm (PyCharm)
echo   - WebStorm (WebStorm)
echo   - PhpStorm (PhpStorm)
echo   - RubyMine (RubyMine)
echo   - CLion (CLion)
echo   - DataGrip (DataGrip)
echo   - GoLand (GoLand)
echo   - Rider (Rider)
echo   - Android Studio (AndroidStudio)
echo.
echo 示例:
echo   %~nx0                          # 默认清理
echo   %~nx0 -d                       # 干运行模式
echo   %~nx0 --ide=PyCharm            # 只清理PyCharm
echo   %~nx0 --chat-only              # 只清理聊天记录
echo   %~nx0 -v --no-backup           # 详细模式，不创建备份
echo.
exit /b 0

REM 获取配置路径
:get_config_paths
set CONFIG_PATHS=%APPDATA%\JetBrains %LOCALAPPDATA%\JetBrains
goto :eof

REM 检查IDE是否正在运行
:check_ide_running
call :log "检查JetBrains IDE是否正在运行..."

set IDE_PROCESSES=idea64.exe idea.exe pycharm64.exe pycharm.exe webstorm64.exe webstorm.exe phpstorm64.exe phpstorm.exe rubymine64.exe rubymine.exe clion64.exe clion.exe datagrip64.exe datagrip.exe goland64.exe goland.exe rider64.exe rider.exe studio64.exe studio.exe

for %%p in (%IDE_PROCESSES%) do (
    tasklist /FI "IMAGENAME eq %%p" 2>NUL | find /I "%%p" >NUL
    if !ERRORLEVEL! EQU 0 (
        call :error "检测到 %%p 正在运行!"
        echo 请先关闭所有JetBrains IDE，然后重新运行此脚本
        exit /b 1
    )
)

call :success "未检测到运行中的JetBrains IDE"
goto :eof

REM 发现已安装的JetBrains产品
:discover_installed_products
call :log "扫描已安装的JetBrains产品..."

set INSTALLED_COUNT=0
set INSTALLED_PRODUCTS=

call :get_config_paths

for %%p in (%CONFIG_PATHS%) do (
    if exist "%%p" (
        for /d %%d in ("%%p\*") do (
            set DIR_NAME=%%~nxd
            for %%j in (%JETBRAINS_PRODUCTS%) do (
                if "!DIR_NAME:~0,%%j!" == "%%j" (
                    set INSTALLED_PRODUCTS=!INSTALLED_PRODUCTS! !DIR_NAME!
                    call :verbose "发现: %%j (!DIR_NAME!)"
                    set /a INSTALLED_COUNT+=1
                )
            )
        )
    )
)

call :log "发现 !INSTALLED_COUNT! 个已安装的JetBrains产品"
goto :eof

REM 创建备份
:create_backup
if "%CREATE_BACKUP%"=="false" goto :eof

call :log "创建备份..."

for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set TIMESTAMP=%dt:~0,4%%dt:~4,2%%dt:~6,2%_%dt:~8,2%%dt:~10,2%%dt:~12,2%

set BACKUP_DIR=%USERPROFILE%\.augment_cleaner_backup\%TIMESTAMP%
mkdir "%BACKUP_DIR%" 2>NUL

call :get_config_paths

for %%p in (%CONFIG_PATHS%) do (
    if exist "%%p" (
        set PATH_NAME=%%~nxp
        set BACKUP_PATH=%BACKUP_DIR%\!PATH_NAME!
        xcopy "%%p" "!BACKUP_PATH!" /E /I /Q >NUL 2>&1
        if !ERRORLEVEL! EQU 0 (
            call :verbose "已备份: !PATH_NAME!"
        ) else (
            call :warn "备份 !PATH_NAME! 失败"
        )
    )
)

REM 创建备份信息文件
echo Augment插件清理备份 v%VERSION% > "%BACKUP_DIR%\backup_info.txt"
echo 创建时间: %date% %time% >> "%BACKUP_DIR%\backup_info.txt"
echo 操作系统: Windows >> "%BACKUP_DIR%\backup_info.txt"
echo 用户: %USERNAME% >> "%BACKUP_DIR%\backup_info.txt"
echo. >> "%BACKUP_DIR%\backup_info.txt"
echo 配置: >> "%BACKUP_DIR%\backup_info.txt"
echo - 干运行模式: %DRY_RUN% >> "%BACKUP_DIR%\backup_info.txt"
echo - 详细模式: %VERBOSE% >> "%BACKUP_DIR%\backup_info.txt"
echo - 安全模式: %SAFE_MODE% >> "%BACKUP_DIR%\backup_info.txt"
echo - 选择IDE: %SELECTED_IDE% >> "%BACKUP_DIR%\backup_info.txt"
echo - 仅聊天记录: %CHAT_ONLY% >> "%BACKUP_DIR%\backup_info.txt"
echo - 保留设置: %KEEP_SETTINGS% >> "%BACKUP_DIR%\backup_info.txt"

REM 尝试压缩备份
where 7z >NUL 2>&1
if !ERRORLEVEL! EQU 0 (
    call :log "压缩备份文件..."
    7z a -tzip "%BACKUP_DIR%.zip" "%BACKUP_DIR%\*" >NUL 2>&1
    if !ERRORLEVEL! EQU 0 (
        rmdir /s /q "%BACKUP_DIR%"
        set BACKUP_CREATED=true
        set BACKUP_PATH=%BACKUP_DIR%.zip
        call :success "备份创建完成: %BACKUP_DIR%.zip"
    ) else (
        call :warn "备份压缩失败，保留未压缩版本"
        set BACKUP_PATH=%BACKUP_DIR%
    )
) else (
    set BACKUP_PATH=%BACKUP_DIR%
    call :success "备份创建完成: %BACKUP_DIR%"
)

set BACKUP_CREATED=true
goto :eof

REM 清理XML配置文件中的Augment属性
:clean_xml_file
set FILE_PATH=%~1

if not exist "%FILE_PATH%" goto :eof

set TEMP_FILE=%TEMP%\augment_clean_%RANDOM%.tmp
set MODIFIED=false

REM 读取文件并移除Augment相关属性
for /f "usebackq delims=" %%l in ("%FILE_PATH%") do (
    set LINE=%%l
    set SKIP_LINE=false
    
    REM 检查是否包含Augment相关属性
    echo !LINE! | findstr /R /C:"property name=\"augment\." /C:"property name=\"Augment\." /C:"property name=\"sessionId" /C:"property name=\"SessionId" /C:"property name=\"installationId" /C:"property name=\"chat\.history" /C:"property name=\"conversation\." /C:"property name=\"feedback\." /C:"property name=\"memory\." /C:"property name=\"context\." /C:"property name=\"preferences\.augment" >NUL
    if !ERRORLEVEL! EQU 0 (
        set SKIP_LINE=true
        set MODIFIED=true
    )
    
    if "!SKIP_LINE!"=="false" (
        echo !LINE! >> "%TEMP_FILE%"
    )
)

REM 如果文件有修改
if "%MODIFIED%"=="true" (
    if "%DRY_RUN%"=="true" (
        call :verbose "[DRY-RUN] 将清理: %FILE_PATH%"
    ) else (
        move "%TEMP_FILE%" "%FILE_PATH%" >NUL 2>&1
        if !ERRORLEVEL! EQU 0 (
            call :verbose "清理: %FILE_PATH%"
            set /a FILES_DELETED+=1
        ) else (
            call :warn "清理 %FILE_PATH% 失败"
        )
    )
) else (
    del "%TEMP_FILE%" 2>NUL
)

goto :eof

REM 清理options目录
:clean_options_dir
set OPTIONS_PATH=%~1

if not exist "%OPTIONS_PATH%" goto :eof

set CONFIG_FILES=other.xml project.default.xml ide.general.xml editor.xml

for %%f in (%CONFIG_FILES%) do (
    call :clean_xml_file "%OPTIONS_PATH%\%%f"
)

REM 删除augment.xml
set AUGMENT_XML=%OPTIONS_PATH%\augment.xml
if exist "%AUGMENT_XML%" (
    if "%DRY_RUN%"=="true" (
        call :verbose "[DRY-RUN] 将删除: %AUGMENT_XML%"
    ) else (
        del "%AUGMENT_XML%" 2>NUL
        if !ERRORLEVEL! EQU 0 (
            call :verbose "删除: %AUGMENT_XML%"
            set /a FILES_DELETED+=1
        )
    )
)

goto :eof

REM 清理插件目录
:clean_plugin_dir
set PLUGIN_PATH=%~1

if not exist "%PLUGIN_PATH%" goto :eof

REM 清理数据目录
set DATA_DIRS=data cache logs tmp

for %%d in (%DATA_DIRS%) do (
    set DIR_PATH=%PLUGIN_PATH%\%%d
    if exist "!DIR_PATH!" (
        if "%DRY_RUN%"=="true" (
            call :verbose "[DRY-RUN] 将删除目录: !DIR_PATH!"
        ) else (
            rmdir /s /q "!DIR_PATH!" 2>NUL
            if !ERRORLEVEL! EQU 0 (
                call :verbose "删除目录: !DIR_PATH!"
                set /a DIRS_DELETED+=1
            )
        )
    )
)

REM 清理特定文件
for /r "%PLUGIN_PATH%" %%f in (*augment* *Augment* *session* *.log *.cache *.tmp) do (
    if exist "%%f" (
        if "%DRY_RUN%"=="true" (
            call :verbose "[DRY-RUN] 将删除: %%f"
        ) else (
            del "%%f" 2>NUL
            if !ERRORLEVEL! EQU 0 (
                call :verbose "删除: %%f"
                set /a FILES_DELETED+=1
            )
        )
    )
)

goto :eof

REM 清理其他Augment文件
:clean_augment_files
set PRODUCT_PATH=%~1

for /r "%PRODUCT_PATH%" %%f in (*augment* *Augment* *session*) do (
    if exist "%%f" (
        REM 跳过插件本体文件
        echo %%f | findstr /C:"\lib\" /C:"\META-INF\" /C:".jar" >NUL
        if !ERRORLEVEL! NEQ 0 (
            if "%DRY_RUN%"=="true" (
                call :verbose "[DRY-RUN] 将删除: %%f"
            ) else (
                del "%%f" 2>NUL
                if !ERRORLEVEL! EQU 0 (
                    call :verbose "删除: %%f"
                    set /a FILES_DELETED+=1
                )
            )
        )
    )
)

goto :eof

REM 清理配置文件
:clean_config_files
call :log "清理配置文件..."

call :discover_installed_products

call :get_config_paths

for %%p in (%CONFIG_PATHS%) do (
    if exist "%%p" (
        for /d %%d in ("%%p\*") do (
            set DIR_NAME=%%~nxd
            set PRODUCT_PATH=%%d
            
            REM 如果指定了特定IDE，跳过其他的
            if defined SELECTED_IDE (
                echo !DIR_NAME! | findstr /C:"%SELECTED_IDE%" >NUL
                if !ERRORLEVEL! NEQ 0 goto :skip_product
            )
            
            REM 检查是否是JetBrains产品
            set IS_JETBRAINS=false
            for %%j in (%JETBRAINS_PRODUCTS%) do (
                if "!DIR_NAME:~0,%%j!" == "%%j" (
                    set IS_JETBRAINS=true
                    call :verbose "清理 %%j 配置..."
                    goto :process_product
                )
            )
            
            :process_product
            if "!IS_JETBRAINS!"=="true" (
                REM 清理options目录
                call :clean_options_dir "!PRODUCT_PATH!\options"
                
                REM 清理插件目录
                call :clean_plugin_dir "!PRODUCT_PATH!\plugins\intellij-augment"
                
                REM 清理其他Augment文件
                call :clean_augment_files "!PRODUCT_PATH!"
                
                set /a CONFIGS_CLEANED+=1
            )
            
            :skip_product
        )
    )
)

call :success "配置文件清理完成，共处理 %CONFIGS_CLEANED% 个产品"
goto :eof

REM 清理缓存文件
:clean_cache_files
call :log "清理缓存文件..."

call :get_config_paths

for %%p in (%CONFIG_PATHS%) do (
    if exist "%%p" (
        REM 只处理缓存目录
        echo %%p | findstr /C:"Local" >NUL
        if !ERRORLEVEL! EQU 0 (
            for /r "%%p" %%f in (*augment* *Augment*) do (
                if exist "%%f" (
                    if "%DRY_RUN%"=="true" (
                        call :verbose "[DRY-RUN] 将删除缓存: %%f"
                    ) else (
                        if exist "%%f\" (
                            rmdir /s /q "%%f" 2>NUL
                            if !ERRORLEVEL! EQU 0 (
                                call :verbose "删除缓存目录: %%f"
                                set /a DIRS_DELETED+=1
                            )
                        ) else (
                            del "%%f" 2>NUL
                            if !ERRORLEVEL! EQU 0 (
                                call :verbose "删除缓存文件: %%f"
                                set /a FILES_DELETED+=1
                            )
                        )
                    )
                )
            )
            set /a CACHES_CLEARED+=1
        )
    )
)

call :success "缓存文件清理完成"
goto :eof

REM 清理项目级数据
:clean_project_data
call :log "清理项目级数据..."

set PROJECT_DIRS=%USERPROFILE%\Projects %USERPROFILE%\workspace %USERPROFILE%\dev %USERPROFILE%\code %USERPROFILE%\Documents %USERPROFILE%\Desktop

for %%d in (%PROJECT_DIRS%) do (
    if exist "%%d" (
        for /r "%%d" %%i in (.idea) do (
            if exist "%%i\" (
                REM 清理.idea目录中的augment目录
                set AUGMENT_DIR=%%i\augment
                if exist "!AUGMENT_DIR!" (
                    if "%DRY_RUN%"=="true" (
                        call :verbose "[DRY-RUN] 将删除项目数据: !AUGMENT_DIR!"
                    ) else (
                        rmdir /s /q "!AUGMENT_DIR!" 2>NUL
                        if !ERRORLEVEL! EQU 0 (
                            call :verbose "删除项目数据: !AUGMENT_DIR!"
                            set /a PROJECTS_CLEANED+=1
                        )
                    )
                )
                
                REM 清理.idea目录中的其他Augment文件
                for /r "%%i" %%f in (*augment* *Augment*) do (
                    if exist "%%f" (
                        if "%DRY_RUN%"=="true" (
                            call :verbose "[DRY-RUN] 将删除: %%f"
                        ) else (
                            del "%%f" 2>NUL
                            if !ERRORLEVEL! EQU 0 (
                                call :verbose "删除: %%f"
                                set /a FILES_DELETED+=1
                            )
                        )
                    )
                )
            )
        )
    )
)

call :success "项目数据清理完成，共处理 %PROJECTS_CLEANED% 个项目"
goto :eof

REM 验证清理结果
:verify_cleanup
call :log "验证清理结果..."

set REMAINING_FILES=0

call :get_config_paths

for %%p in (%CONFIG_PATHS%) do (
    if exist "%%p" (
        for /r "%%p" %%f in (*augment*) do (
            if exist "%%f" (
                REM 跳过目录
                if not exist "%%f\" (
                    set /a REMAINING_FILES+=1
                    call :verbose "残留文件: %%f"
                )
            )
        )
    )
)

if %REMAINING_FILES% EQU 0 (
    call :success "清理验证通过！未发现残留文件"
) else (
    call :warn "仍有 %REMAINING_FILES% 个相关文件未清理"
    call :info "这可能是正常的（如插件本身的文件）"
)

goto :eof

REM 生成清理报告
:generate_report
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set TIMESTAMP=%dt:~0,4%%dt:~4,2%%dt:~6,2%_%dt:~8,2%%dt:~10,2%%dt:~12,2%

set REPORT_FILE=%USERPROFILE%\.augment_cleaner_report_%TIMESTAMP%.txt

echo Augment插件清理报告 v%VERSION% > "%REPORT_FILE%"
echo ================== >> "%REPORT_FILE%"
echo. >> "%REPORT_FILE%"
echo 清理时间: %date% %time% >> "%REPORT_FILE%"
echo 操作系统: Windows >> "%REPORT_FILE%"
echo 用户: %USERNAME% >> "%REPORT_FILE%"
echo. >> "%REPORT_FILE%"
echo 配置: >> "%REPORT_FILE%"
echo - 干运行模式: %DRY_RUN% >> "%REPORT_FILE%"
echo - 详细模式: %VERBOSE% >> "%REPORT_FILE%"
echo - 安全模式: %SAFE_MODE% >> "%REPORT_FILE%"
echo - 选择IDE: %SELECTED_IDE% >> "%REPORT_FILE%"
echo - 仅聊天记录: %CHAT_ONLY% >> "%REPORT_FILE%"
echo - 保留设置: %KEEP_SETTINGS% >> "%REPORT_FILE%"
echo. >> "%REPORT_FILE%"
echo 清理统计: >> "%REPORT_FILE%"
echo - 删除文件: %FILES_DELETED% >> "%REPORT_FILE%"
echo - 删除目录: %DIRS_DELETED% >> "%REPORT_FILE%"
echo - 清理配置: %CONFIGS_CLEANED% >> "%REPORT_FILE%"
echo - 清理缓存: %CACHES_CLEARED% >> "%REPORT_FILE%"
echo - 清理项目: %PROJECTS_CLEANED% >> "%REPORT_FILE%"
echo. >> "%REPORT_FILE%"
echo 备份信息: >> "%REPORT_FILE%"
echo - 备份创建: %BACKUP_CREATED% >> "%REPORT_FILE%"
echo - 备份位置: %BACKUP_PATH% >> "%REPORT_FILE%"

call :success "清理报告已生成: %REPORT_FILE%"
goto :eof

REM 交互式配置
:interactive_config
if "%SILENT_MODE%"=="true" goto :eof

echo %PURPLE%=== Augment插件清理工具配置 ===%NC%
echo.

REM 显示发现的产品
call :discover_installed_products

if %INSTALLED_COUNT% GTR 0 (
    echo 发现的JetBrains产品:
    set COUNT=1
    for %%p in (%INSTALLED_PRODUCTS%) do (
        echo   !COUNT!. %%p
        set /a COUNT+=1
    )
    echo.
)

REM 询问清理范围
if "%SELECTED_IDE%"=="" if "%CHAT_ONLY%"=="false" (
    echo 选择清理范围:
    echo   1. 全部产品
    echo   2. 仅聊天记录
    echo   3. 指定IDE
    set /p CHOICE="请选择 (1-3) [1]: "
    
    if "!CHOICE!"=="2" (
        set CHAT_ONLY=true
        call :info "✓ 将只清理聊天记录"
    ) else if "!CHOICE!"=="3" (
        echo.
        echo 可选的IDE:
        for %%j in (%JETBRAINS_PRODUCTS%) do (
            echo   - %%j
        )
        set /p IDE_NAME="请输入IDE名称: "
        set SELECTED_IDE=!IDE_NAME!
        call :info "✓ 将只清理 !SELECTED_IDE!"
    ) else (
        call :info "✓ 将清理所有JetBrains产品的Augment数据"
    )
    echo.
)

REM 询问是否创建备份
if "%CREATE_BACKUP%"=="true" (
    set /p BACKUP_CHOICE="是否创建备份? (Y/n): "
    if /i "!BACKUP_CHOICE!"=="n" (
        set CREATE_BACKUP=false
        call :info "✓ 将不创建备份"
    ) else (
        call :info "✓ 将创建备份"
    )
    echo.
)

REM 询问是否保留设置
if "%KEEP_SETTINGS%"=="false" (
    set /p KEEP_CHOICE="是否保留用户设置? (y/N): "
    if /i "!KEEP_CHOICE!"=="y" (
        set KEEP_SETTINGS=true
        call :info "✓ 将保留用户设置"
    )
    echo.
)

goto :eof

REM 主函数
:main
REM 显示标题
if "%SILENT_MODE%"=="false" (
    echo.
    echo  █████╗ ██╗   ██╗ ██████╗ ███╗   ███╗███████╗███╗   ██╗████████╗
    echo ██╔══██╗██║   ██║██╔════╝ ████╗ ████║██╔════╝████╗  ██║╚══██╔══╝
    echo ███████║██║   ██║██║  ███╗██╔████╔██║█████╗  ██╔██╗ ██║   ██║
    echo ██╔══██║██║   ██║██║   ██║██║╚██╔╝██║██╔══╝  ██║╚██╗██║   ██║
    echo ██║  ██║╚██████╔╝╚██████╔╝██║ ╚═╝ ██║███████╗██║ ╚████║   ██║
    echo ╚═╝  ╚═╝ ╚═════╝  ╚═════╝ ╚═╝     ╚═╝╚══════╝╚═╝  ╚═══╝   ╚═╝
    echo.
    echo                     %SCRIPT_NAME% v%VERSION%
    echo                   支持全系列JetBrains产品
    echo                   关注公众号：煎饼果子卷AI
    echo ========================================
    echo.
)

REM 检查环境
call :check_ide_running

REM 交互式配置
call :interactive_config

REM 显示警告和确认
if "%SILENT_MODE%"=="false" if "%DRY_RUN%"=="false" (
    call :warn "此工具将清理Augment插件的所有跟踪数据"
    call :warn "包括聊天记录、会话数据、配置文件等"
    echo.
    
    set /p CONFIRM="确认继续? (y/N): "
    if /i not "!CONFIRM!"=="y" (
        echo 清理已取消
        exit /b 0
    )
    echo.
)

call :log "开始清理Augment插件数据..."

REM 创建备份
call :create_backup

REM 执行清理
call :clean_config_files
call :clean_cache_files
call :clean_project_data

REM 验证结果
call :verify_cleanup

REM 生成报告
call :generate_report

REM 显示结果摘要
if "%SILENT_MODE%"=="false" (
    echo.
    echo === 清理摘要 ===
    echo 删除文件: %FILES_DELETED%
    echo 删除目录: %DIRS_DELETED%
    echo 清理配置: %CONFIGS_CLEANED%
    echo 清理缓存: %CACHES_CLEARED%
    echo 清理项目: %PROJECTS_CLEANED%
    
    if "%BACKUP_CREATED%"=="true" (
        echo 备份位置: %BACKUP_PATH%
    )
    
    echo.
    echo ========================================
    echo ██████╗ ██╗   ██╗██████╗ ██████╗ ████████╗ █████╗ ███╗   ██╗████████╗
    echo ██╔══██╗██║   ██║██╔══██╗██╔══██╗╚══██╔══╝██╔══██╗████╗  ██║╚══██╔══╝
    echo ██║  ██║██║   ██║██████╔╝██████╔╝   ██║   ███████║██╔██╗ ██║   ██║
    echo ██║  ██║██║   ██║██╔══██╗██╔══██╗   ██║   ██╔══██║██║╚██╗██║   ██║
    echo ██████╔╝╚██████╔╝██║  ██║██║  ██║   ██║   ██║  ██║██║ ╚████║   ██║
    echo ╚═════╝  ╚═════╝ ╚═╝  ╚═╝╚═╝  ╚═╝   ╚═╝   ╚═╝  ╚═╝╚═╝  ╚═══╝   ╚═╝
    echo.
    echo                            【重要】
    echo %RED%🔥🔥🔥 强烈建议手动删除项目里面 .idea，然后重新打开项目能避免二次风控！ 🔥🔥🔥%NC%
    echo                        关注公众号：煎饼果子卷AI
    echo ========================================
    echo.
    echo 下一步:
    echo 1. 重启JetBrains IDE
    echo 2. Augment插件将以全新状态启动
    echo 3. 如有问题，可使用备份文件恢复

    if "%DRY_RUN%"=="true" (
        echo.
        call :warn "注意: 这是干运行模式，实际上没有删除任何文件"
    )
)

call :success "Augment插件清理完成！"
goto :eof

REM 运行主函数
call :main

