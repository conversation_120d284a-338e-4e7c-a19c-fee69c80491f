# 编译输出目录
dist/
build/
out/
target/
bin/
obj/

# 依赖管理
node_modules/
vendor/
.pnp
.pnp.js

# 日志文件
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
lib-cov
coverage/
*.lcov
.nyc_output

# 缓存目录
.cache/
.parcel-cache/
.npm
.eslintcache
.stylelintcache

# 可选的 npm 缓存目录
.npm

# 可选的 REPL 历史
.node_repl_history

# 输出的 npm 包
*.tgz

# Yarn 完整性文件
.yarn-integrity

# dotenv 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler 缓存 (https://parceljs.org/)
.cache
.parcel-cache

# Next.js 构建输出
.next
out

# Nuxt.js 构建 / 生成输出
.nuxt
dist

# Gatsby 文件
.cache/
public

# Vuepress 构建输出
.vuepress/dist

# Serverless 目录
.serverless/

# FuseBox 缓存
.fusebox/

# DynamoDB Local 文件
.dynamodb/

# TernJS 端口文件
.tern-port

# Stores VSCode 版本用于测试 Electron-prebuilt
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# IDE 和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows 图像文件缓存
Thumbs.db
ehthumbs.db

# 文件夹配置文件
Desktop.ini

# 回收站在 OneDrive 上使用的文件
$RECYCLE.BIN/

# VS Code 目录
.vscode/

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# 临时文件
*.tmp
*.temp
*.bak
*.backup
*.old

# 压缩文件
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# 数据库
*.db
*.sqlite
*.sqlite3

# 配置文件（可能包含敏感信息）
config.json
config.yml
config.yaml
secrets.json

# 测试覆盖率
coverage/
*.cover
.hypothesis/
.pytest_cache/

# 文档构建
docs/_build/

# PyInstaller
*.manifest
*.spec

# 单元测试 / 覆盖率报告
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/

# Go 相关
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Java 相关
*.class
*.jar
*.war
*.ear
*.nar
hs_err_pid*

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# Gradle
.gradle
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

# Eclipse
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# VS Code
.vscode/

# Mac
.DS_Store

# 自定义忽略
# 添加您项目特定的忽略文件和目录
