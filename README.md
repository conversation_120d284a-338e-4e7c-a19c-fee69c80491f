# Augment插件清理工具使用指南

## 🎯 工具概述

Augment插件清理工具是一套专门设计的工具集，用于彻底清理Augment插件的所有跟踪数据，让插件以为运行在全新的环境中。工具包含多个版本以适应不同的使用场景和技术水平。

### 🛠️ 工具版本

1. **Go语言版本** - 功能最全面，适合技术用户
2. **Shell脚本版本** - 轻量级，适合Linux/macOS用户
3. **批处理版本** - 简单易用，适合Windows用户

## 📋 清理范围

### 完全清理的数据类型

✅ **设备标识数据**
- PermanentInstallationID相关配置
- SessionID和会话数据
- 用户代理字符串缓存

✅ **配置文件**
- PropertiesComponent中的Augment设置
- IDE选项文件中的相关配置
- 插件状态和偏好设置

✅ **缓存和临时文件**
- 本地索引缓存
- Blob状态文件
- 临时处理文件

✅ **日志和跟踪数据**
- 操作日志文件
- 错误和异常记录
- Sentry监控数据

✅ **项目级数据**
- .idea目录中的Augment文件
- 项目特定的配置和缓存
- 记忆和上下文数据

### 保留的数据

❌ **插件本体** - 不删除插件程序文件
❌ **用户代码** - 不影响用户的源代码
❌ **IDE设置** - 不影响其他IDE配置

---

## 🚀 快速开始

### 方法1: 一键脚本（推荐新手）

#### Windows用户
```cmd
# 下载并运行
augment_cleaner.bat
```

#### Linux/macOS用户
```bash
# 下载并运行
chmod +x augment_cleaner.sh
./augment_cleaner.sh
```

### 方法2: Go程序（推荐高级用户）

#### Windows
```cmd
augment-cleaner-windows-amd64.exe
```

#### Linux
```bash
./augment-cleaner-linux-amd64
```

#### macOS
```bash
./augment-cleaner-darwin-amd64
```

---

## 📖 详细使用说明

### 使用前准备

1. **关闭IntelliJ IDEA**
   ```
   确保所有IntelliJ IDEA进程都已关闭
   工具会自动检测并提示
   ```

2. **备份重要数据**（可选但推荐）
   ```
   工具提供自动备份功能
   备份文件保存在用户目录下
   ```

3. **确认清理范围**
   ```
   工具会显示将要清理的文件列表
   用户可以选择性确认
   ```

### Go程序详细使用

#### 基本用法
```bash
# 默认模式：安全清理 + 自动备份
./augment-cleaner-linux-amd64

# 查看帮助
./augment-cleaner-linux-amd64 -h
```

#### 高级选项
```bash
# 干运行模式（只显示将要清理的文件，不实际删除）
./augment-cleaner-linux-amd64 -dry-run

# 禁用备份（不推荐）
./augment-cleaner-linux-amd64 -no-backup

# 非安全模式（清理更彻底，但风险更高）
./augment-cleaner-linux-amd64 -unsafe

# 静默模式（无交互）
./augment-cleaner-linux-amd64 -silent
```

#### 组合使用
```bash
# 先查看将要清理什么
./augment-cleaner-linux-amd64 -dry-run

# 确认后执行实际清理
./augment-cleaner-linux-amd64
```

### Shell脚本使用

#### Linux/macOS
```bash
# 基本使用
./augment_cleaner.sh

# 脚本会引导用户完成以下步骤：
# 1. 环境检查
# 2. 确认清理范围
# 3. 选择是否备份
# 4. 执行清理
# 5. 验证结果
```

#### 脚本特性
- ✅ 彩色输出，易于阅读
- ✅ 详细的进度提示
- ✅ 自动环境检测
- ✅ 安全检查机制
- ✅ 完整的错误处理

### Windows批处理使用

#### 基本使用
```cmd
# 双击运行或命令行执行
augment_cleaner.bat

# 批处理会自动：
# 1. 检查管理员权限
# 2. 检测IDE运行状态
# 3. 引导用户确认
# 4. 执行清理操作
# 5. 生成清理报告
```

---

## 🔧 高级功能

### 自定义清理范围

#### 修改Go程序配置
```go
// 在augment_cleaner.go中修改
cleaner := NewAugmentCleaner()
cleaner.SafeMode = false      // 禁用安全模式
cleaner.BackupEnabled = false // 禁用备份
cleaner.DryRun = true        // 启用干运行
```

#### 修改脚本行为
```bash
# 在脚本中修改变量
SAFE_MODE=true
CREATE_BACKUP=true
VERBOSE=true
```

### 批量处理

#### 处理多个用户配置
```bash
# 为多个用户清理（需要管理员权限）
for user in user1 user2 user3; do
    sudo -u $user ./augment_cleaner.sh
done
```

#### 企业环境部署
```bash
# 创建企业配置文件
cat > enterprise_config.sh << 'EOF'
SAFE_MODE=true
CREATE_BACKUP=true
SILENT_MODE=true
ENTERPRISE_MODE=true
EOF

# 使用企业配置
source enterprise_config.sh && ./augment_cleaner.sh
```

---

## 🛡️ 安全特性

### 多层安全保护

1. **运行时检查**
   - IDE进程检测
   - 文件锁定检查
   - 权限验证

2. **数据保护**
   - 自动备份机制
   - 增量备份支持
   - 压缩存储

3. **回滚支持**
   - 一键恢复功能
   - 配置文件恢复
   - 状态验证

### 备份和恢复

#### 自动备份
```
备份位置：
- Windows: %USERPROFILE%\.augment_cleaner_backup\
- Linux/macOS: ~/.augment_cleaner_backup/

备份内容：
- 所有配置文件
- 重要的缓存数据
- 用户设置
- 清理清单
```

#### 手动恢复
```bash
# 解压备份文件
tar -xzf ~/.augment_cleaner_backup/20231215_143022.tar.gz

# 恢复配置文件
cp -r backup_dir/* ~/.config/JetBrains/

# 重启IDE验证
```

#### 自动恢复脚本
```bash
# 工具会生成恢复脚本
~/.augment_cleaner_backup/restore_20231215_143022.sh
```

---

## 🔍 故障排除

### 常见问题

#### 1. "IDE正在运行"错误
```
问题：工具检测到IntelliJ IDEA正在运行
解决：
1. 完全关闭IntelliJ IDEA
2. 检查任务管理器中的相关进程
3. 重启计算机（如果进程无法关闭）
```

#### 2. 权限不足错误
```
问题：无法删除某些文件
解决：
1. 以管理员身份运行（Windows）
2. 使用sudo运行（Linux/macOS）
3. 检查文件是否被其他程序占用
```

#### 3. 备份失败
```
问题：无法创建备份
解决：
1. 检查磁盘空间
2. 确认备份目录权限
3. 手动创建备份目录
```

#### 4. 清理不完整
```
问题：仍有残留文件
解决：
1. 运行验证功能
2. 手动检查残留文件
3. 使用非安全模式重新清理
```

### 高级故障排除

#### 启用详细日志
```bash
# Go程序
./augment-cleaner-linux-amd64 -verbose

# Shell脚本
VERBOSE=true ./augment_cleaner.sh
```

#### 手动清理残留
```bash
# 查找残留文件
find ~ -name "*augment*" -o -name "*Augment*" 2>/dev/null

# 手动删除（谨慎操作）
find ~ -name "*augment*" -type f -delete 2>/dev/null
```

#### 重置IDE配置
```bash
# 完全重置IntelliJ IDEA配置（慎用）
rm -rf ~/.config/JetBrains/IntelliJIdea*
rm -rf ~/.local/share/JetBrains/IntelliJIdea*
```

---

## ⚠️ 重要注意事项

### 使用前必读

1. **数据安全**
   - 清理操作不可逆
   - 强烈建议创建备份
   - 重要项目请额外备份

2. **功能影响**
   - 插件需要重新初始化
   - 可能需要重新登录
   - 个性化设置会丢失

3. **系统兼容性**
   - 支持Windows 10+
   - 支持macOS 10.14+
   - 支持主流Linux发行版

### 企业使用建议

1. **测试环境验证**
   ```
   在测试环境中先验证清理效果
   确认不会影响业务流程
   制定应急恢复方案
   ```

2. **批量部署**
   ```
   使用配置管理工具
   统一的清理策略
   集中的日志收集
   ```

3. **合规性考虑**
   ```
   确认符合公司政策
   记录清理操作
   保留必要的审计日志
   ```

---

## 📊 清理效果验证

### 验证方法

1. **自动验证**
   ```
   工具内置验证功能
   检查残留文件
   验证配置重置
   ```

2. **手动验证**
   ```bash
   # 检查配置目录
   ls -la ~/.config/JetBrains/IntelliJIdea*/options/
   
   # 搜索残留文件
   find ~ -name "*augment*" 2>/dev/null
   
   # 检查进程
   ps aux | grep augment
   ```

3. **IDE验证**
   ```
   重启IntelliJ IDEA
   检查插件状态
   验证是否需要重新登录
   确认功能正常
   ```

### 成功标志

✅ **完全清理成功**
- 无残留配置文件
- 插件状态重置
- 需要重新初始化
- 生成新的SessionID

✅ **部分清理成功**
- 主要数据已清理
- 少量无关紧要的残留
- 插件功能正常
- 达到预期效果

❌ **清理失败**
- 大量文件未清理
- 插件状态未改变
- 仍使用旧的SessionID
- 需要重新清理

---

## 🔄 更新和维护

### 工具更新

1. **检查更新**
   ```
   定期检查新版本
   关注安全补丁
   更新清理规则
   ```

2. **版本兼容性**
   ```
   支持多个IDE版本
   适配新的插件版本
   保持向后兼容
   ```

### 社区支持

1. **问题反馈**
   ```
   报告清理问题
   提供改进建议
   分享使用经验
   ```

2. **贡献代码**
   ```
   提交bug修复
   添加新功能
   改进文档
   ```

---


---

**免责声明：本工具仅供学习和研究使用，使用前请确保符合相关法律法规和软件许可协议。使用本工具造成的任何损失，开发者不承担责任。**

