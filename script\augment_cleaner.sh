#!/bin/bash

# Augment插件清理工具 v2.0 - Linux/macOS版本
# 支持全系列JetBrains产品的Augment插件数据清理

set -e

# 版本信息
VERSION="2.0"
SCRIPT_NAME="Augment插件清理工具"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
DRY_RUN=false
VERBOSE=false
SAFE_MODE=true
CREATE_BACKUP=true
SILENT_MODE=false
SELECTED_IDE=""
CHAT_ONLY=false
KEEP_SETTINGS=false

# 统计变量
FILES_DELETED=0
DIRS_DELETED=0
CONFIGS_CLEANED=0
CACHES_CLEARED=0
PROJECTS_CLEANED=0
BACKUP_CREATED=false
BACKUP_PATH=""
START_TIME=$(date +%s)

# JetBrains产品列表
declare -A JETBRAINS_PRODUCTS=(
    ["IntelliJIdea"]="IntelliJ IDEA"
    ["PyCharm"]="PyCharm"
    ["WebStorm"]="WebStorm"
    ["PhpStorm"]="PhpStorm"
    ["RubyMine"]="RubyMine"
    ["CLion"]="CLion"
    ["DataGrip"]="DataGrip"
    ["GoLand"]="GoLand"
    ["Rider"]="Rider"
    ["AndroidStudio"]="Android Studio"
)

# Augment相关的配置属性模式
AUGMENT_PATTERNS=(
    "augment\\."
    "Augment\\."
    "AUGMENT_"
    "sessionId"
    "SessionId"
    "installationId"
    "chat\\.history"
    "conversation\\."
    "feedback\\."
    "memory\\."
    "context\\."
    "preferences\\.augment"
)

# 需要清理的文件模式
CLEANUP_PATTERNS=(
    "*augment*"
    "*Augment*"
    "*session*"
    "*Session*"
    "chat_history.*"
    "conversations.*"
    "augment.xml"
    "augment.log"
    "*.augment.cache"
)

# 日志函数
log() {
    if [[ "$SILENT_MODE" != "true" ]]; then
        echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
    fi
}

verbose() {
    if [[ "$VERBOSE" == "true" && "$SILENT_MODE" != "true" ]]; then
        echo -e "  ${CYAN}→${NC} $1"
    fi
}

warn() {
    if [[ "$SILENT_MODE" != "true" ]]; then
        echo -e "${YELLOW}⚠️  $1${NC}"
    fi
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

success() {
    if [[ "$SILENT_MODE" != "true" ]]; then
        echo -e "${GREEN}✅ $1${NC}"
    fi
}

info() {
    if [[ "$SILENT_MODE" != "true" ]]; then
        echo -e "${PURPLE}ℹ️  $1${NC}"
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
$SCRIPT_NAME v$VERSION
支持全系列JetBrains产品的Augment插件数据清理

用法:
  $0 [选项]

选项:
  -h, --help          显示帮助信息
  -v, --verbose       详细输出模式
  -d, --dry-run       干运行模式（只显示将要清理的文件）
  -s, --silent        静默模式（无交互）
  --no-backup         禁用备份
  --unsafe            非安全模式（更彻底的清理）
  --ide=NAME          只清理指定的IDE（如：IntelliJIdea）
  --chat-only         只清理聊天记录
  --keep-settings     保留用户设置

支持的IDE:
$(for product in "${!JETBRAINS_PRODUCTS[@]}"; do
    echo "  - ${JETBRAINS_PRODUCTS[$product]} ($product)"
done)

示例:
  $0                          # 默认清理
  $0 -d                       # 干运行模式
  $0 --ide=PyCharm            # 只清理PyCharm
  $0 --chat-only              # 只清理聊天记录
  $0 -v --no-backup           # 详细模式，不创建备份

EOF
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -s|--silent)
                SILENT_MODE=true
                shift
                ;;
            --no-backup)
                CREATE_BACKUP=false
                shift
                ;;
            --unsafe)
                SAFE_MODE=false
                shift
                ;;
            --chat-only)
                CHAT_ONLY=true
                shift
                ;;
            --keep-settings)
                KEEP_SETTINGS=true
                shift
                ;;
            --ide=*)
                SELECTED_IDE="${1#*=}"
                shift
                ;;
            *)
                error "未知选项: $1"
                echo "使用 -h 或 --help 查看帮助信息"
                exit 1
                ;;
        esac
    done
}

# 获取配置路径
get_config_paths() {
    local paths=()
    
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        paths+=(
            "$HOME/Library/Application Support/JetBrains"
            "$HOME/Library/Preferences/JetBrains"
            "$HOME/Library/Caches/JetBrains"
            "$HOME/Library/Logs/JetBrains"
        )
    else
        # Linux
        paths+=(
            "$HOME/.config/JetBrains"
            "$HOME/.local/share/JetBrains"
            "$HOME/.cache/JetBrains"
        )
    fi
    
    printf '%s\n' "${paths[@]}"
}

# 检查IDE是否正在运行
check_ide_running() {
    log "检查JetBrains IDE是否正在运行..."
    
    local processes=(
        "idea" "intellij"
        "pycharm"
        "webstorm"
        "phpstorm"
        "rubymine"
        "clion"
        "datagrip"
        "goland"
        "rider"
        "android-studio" "studio"
        "fleet"
    )
    
    for proc in "${processes[@]}"; do
        if pgrep -f "$proc" > /dev/null 2>&1; then
            error "检测到 $proc 正在运行!"
            echo "请先关闭所有JetBrains IDE，然后重新运行此脚本"
            exit 1
        fi
    done
    
    success "未检测到运行中的JetBrains IDE"
}

# 发现已安装的JetBrains产品
discover_installed_products() {
    log "扫描已安装的JetBrains产品..."
    
    local installed_products=()
    local config_paths
    readarray -t config_paths < <(get_config_paths)
    
    for config_path in "${config_paths[@]}"; do
        if [[ ! -d "$config_path" ]]; then
            continue
        fi
        
        for dir in "$config_path"/*; do
            if [[ ! -d "$dir" ]]; then
                continue
            fi
            
            local dir_name=$(basename "$dir")
            for product in "${!JETBRAINS_PRODUCTS[@]}"; do
                if [[ "$dir_name" == "$product"* ]]; then
                    # 检查是否已经添加过
                    local found=false
                    for installed in "${installed_products[@]}"; do
                        if [[ "$installed" == "$dir_name" ]]; then
                            found=true
                            break
                        fi
                    done
                    
                    if [[ "$found" == "false" ]]; then
                        installed_products+=("$dir_name")
                        verbose "发现: ${JETBRAINS_PRODUCTS[$product]} ($dir_name)"
                    fi
                fi
            done
        done
    done
    
    log "发现 ${#installed_products[@]} 个已安装的JetBrains产品"
    printf '%s\n' "${installed_products[@]}"
}

# 创建备份
create_backup() {
    if [[ "$CREATE_BACKUP" != "true" ]]; then
        return 0
    fi
    
    log "创建备份..."
    
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_dir="$HOME/.augment_cleaner_backup/$timestamp"
    
    mkdir -p "$backup_dir"
    
    # 备份配置文件
    local config_paths
    readarray -t config_paths < <(get_config_paths)
    
    for config_path in "${config_paths[@]}"; do
        if [[ ! -d "$config_path" ]]; then
            continue
        fi
        
        local path_name=$(basename "$config_path")
        local backup_path="$backup_dir/$path_name"
        
        if cp -r "$config_path" "$backup_path" 2>/dev/null; then
            verbose "已备份: $path_name"
        else
            warn "备份 $path_name 失败"
        fi
    done
    
    # 创建备份信息文件
    cat > "$backup_dir/backup_info.txt" << EOF
Augment插件清理备份 v$VERSION
创建时间: $(date)
操作系统: $OSTYPE
用户: $USER
主目录: $HOME

配置:
- 干运行模式: $DRY_RUN
- 详细模式: $VERBOSE
- 安全模式: $SAFE_MODE
- 选择IDE: $SELECTED_IDE
- 仅聊天记录: $CHAT_ONLY
- 保留设置: $KEEP_SETTINGS

备份内容:
- JetBrains配置文件
- Augment插件相关文件
- 会话和缓存数据

恢复方法:
1. 关闭JetBrains IDE
2. 将备份文件复制回原位置
3. 重启IDE
EOF
    
    # 压缩备份
    if command -v tar >/dev/null 2>&1; then
        log "压缩备份文件..."
        local backup_archive="$backup_dir.tar.gz"
        if tar -czf "$backup_archive" -C "$(dirname "$backup_dir")" "$(basename "$backup_dir")" 2>/dev/null; then
            rm -rf "$backup_dir"
            BACKUP_CREATED=true
            BACKUP_PATH="$backup_archive"
            success "备份创建完成: $backup_archive"
        else
            warn "备份压缩失败，保留未压缩版本"
            BACKUP_PATH="$backup_dir"
        fi
    else
        BACKUP_PATH="$backup_dir"
        success "备份创建完成: $backup_dir"
    fi
}

# 清理XML配置文件中的Augment属性
clean_xml_file() {
    local file_path="$1"
    
    if [[ ! -f "$file_path" ]]; then
        return 0
    fi
    
    local temp_file=$(mktemp)
    local modified=false
    
    # 读取文件并移除Augment相关属性
    while IFS= read -r line; do
        local skip_line=false
        
        for pattern in "${AUGMENT_PATTERNS[@]}"; do
            if echo "$line" | grep -qE "property name=\"$pattern"; then
                skip_line=true
                modified=true
                break
            fi
        done
        
        if [[ "$skip_line" == "false" ]]; then
            echo "$line" >> "$temp_file"
        fi
    done < "$file_path"
    
    # 如果文件有修改
    if [[ "$modified" == "true" ]]; then
        if [[ "$DRY_RUN" == "true" ]]; then
            verbose "[DRY-RUN] 将清理: $file_path"
        else
            if mv "$temp_file" "$file_path"; then
                verbose "清理: $file_path"
                ((FILES_DELETED++))
            else
                warn "清理 $file_path 失败"
            fi
        fi
    fi
    
    rm -f "$temp_file"
}

# 清理options目录
clean_options_dir() {
    local options_path="$1"
    
    if [[ ! -d "$options_path" ]]; then
        return 0
    fi
    
    local config_files=("other.xml" "project.default.xml" "ide.general.xml" "editor.xml")
    
    for config_file in "${config_files[@]}"; do
        local file_path="$options_path/$config_file"
        clean_xml_file "$file_path"
    done
    
    # 删除augment.xml
    local augment_xml="$options_path/augment.xml"
    if [[ -f "$augment_xml" ]]; then
        if [[ "$DRY_RUN" == "true" ]]; then
            verbose "[DRY-RUN] 将删除: $augment_xml"
        else
            if rm -f "$augment_xml"; then
                verbose "删除: $augment_xml"
                ((FILES_DELETED++))
            fi
        fi
    fi
}

# 清理插件目录
clean_plugin_dir() {
    local plugin_path="$1"
    
    if [[ ! -d "$plugin_path" ]]; then
        return 0
    fi
    
    # 清理数据目录
    local data_dirs=("data" "cache" "logs" "tmp")
    for dir in "${data_dirs[@]}"; do
        local dir_path="$plugin_path/$dir"
        if [[ -d "$dir_path" ]]; then
            if [[ "$DRY_RUN" == "true" ]]; then
                verbose "[DRY-RUN] 将删除目录: $dir_path"
            else
                if rm -rf "$dir_path"; then
                    verbose "删除目录: $dir_path"
                    ((DIRS_DELETED++))
                fi
            fi
        fi
    done
    
    # 清理特定文件
    find "$plugin_path" -type f \( -name "*augment*" -o -name "*Augment*" -o -name "*session*" -o -name "*.log" -o -name "*.cache" -o -name "*.tmp" \) 2>/dev/null | while read -r file; do
        if [[ "$DRY_RUN" == "true" ]]; then
            verbose "[DRY-RUN] 将删除: $file"
        else
            if rm -f "$file"; then
                verbose "删除: $file"
                ((FILES_DELETED++))
            fi
        fi
    done
}

# 清理其他Augment文件
clean_augment_files() {
    local product_path="$1"
    
    find "$product_path" -type f \( -name "*augment*" -o -name "*Augment*" -o -name "*session*" \) 2>/dev/null | while read -r file; do
        # 跳过插件本体文件
        if echo "$file" | grep -q "/lib/\|/META-INF/\|\.jar$"; then
            continue
        fi
        
        if [[ "$DRY_RUN" == "true" ]]; then
            verbose "[DRY-RUN] 将删除: $file"
        else
            if rm -f "$file"; then
                verbose "删除: $file"
                ((FILES_DELETED++))
            fi
        fi
    done
}

# 清理配置文件
clean_config_files() {
    log "清理配置文件..."
    
    local installed_products
    readarray -t installed_products < <(discover_installed_products)
    
    local config_paths
    readarray -t config_paths < <(get_config_paths)
    
    for product in "${installed_products[@]}"; do
        # 如果指定了特定IDE，跳过其他的
        if [[ -n "$SELECTED_IDE" && "$product" != *"$SELECTED_IDE"* ]]; then
            continue
        fi
        
        # 获取产品显示名称
        local display_name="$product"
        for key in "${!JETBRAINS_PRODUCTS[@]}"; do
            if [[ "$product" == "$key"* ]]; then
                display_name="${JETBRAINS_PRODUCTS[$key]}"
                break
            fi
        done
        
        verbose "清理 $display_name 配置..."
        
        for config_path in "${config_paths[@]}"; do
            local product_path="$config_path/$product"
            if [[ ! -d "$product_path" ]]; then
                continue
            fi
            
            # 清理options目录
            local options_path="$product_path/options"
            clean_options_dir "$options_path"
            
            # 清理插件目录
            local plugin_path="$product_path/plugins/intellij-augment"
            clean_plugin_dir "$plugin_path"
            
            # 清理其他Augment文件
            clean_augment_files "$product_path"
        done
        
        ((CONFIGS_CLEANED++))
    done
    
    success "配置文件清理完成，共处理 $CONFIGS_CLEANED 个产品"
}

# 清理缓存文件
clean_cache_files() {
    log "清理缓存文件..."
    
    local config_paths
    readarray -t config_paths < <(get_config_paths)
    
    for config_path in "${config_paths[@]}"; do
        if [[ ! -d "$config_path" ]]; then
            continue
        fi
        
        # 只处理缓存和日志目录
        if [[ "$config_path" != *"Cache"* && "$config_path" != *"cache"* && "$config_path" != *"Log"* ]]; then
            continue
        fi
        
        find "$config_path" \( -name "*augment*" -o -name "*Augment*" \) 2>/dev/null | while read -r item; do
            if [[ "$DRY_RUN" == "true" ]]; then
                verbose "[DRY-RUN] 将删除缓存: $item"
            else
                if [[ -d "$item" ]]; then
                    if rm -rf "$item"; then
                        verbose "删除缓存目录: $item"
                        ((DIRS_DELETED++))
                    fi
                else
                    if rm -f "$item"; then
                        verbose "删除缓存文件: $item"
                        ((FILES_DELETED++))
                    fi
                fi
            fi
        done
        
        ((CACHES_CLEARED++))
    done
    
    success "缓存文件清理完成"
}

# 清理项目级数据
clean_project_data() {
    log "清理项目级数据..."
    
    local project_dirs=(
        "$HOME/Projects"
        "$HOME/workspace"
        "$HOME/dev"
        "$HOME/code"
        "$HOME/Documents"
        "$HOME/Desktop"
    )
    
    for project_dir in "${project_dirs[@]}"; do
        if [[ ! -d "$project_dir" ]]; then
            continue
        fi
        
        find "$project_dir" -name ".idea" -type d 2>/dev/null | while read -r idea_dir; do
            # 清理.idea目录中的augment目录
            local augment_dir="$idea_dir/augment"
            if [[ -d "$augment_dir" ]]; then
                if [[ "$DRY_RUN" == "true" ]]; then
                    verbose "[DRY-RUN] 将删除项目数据: $augment_dir"
                else
                    if rm -rf "$augment_dir"; then
                        verbose "删除项目数据: $augment_dir"
                        ((PROJECTS_CLEANED++))
                    fi
                fi
            fi
            
            # 清理.idea目录中的其他Augment文件
            find "$idea_dir" -type f \( -name "*augment*" -o -name "*Augment*" \) 2>/dev/null | while read -r file; do
                if [[ "$DRY_RUN" == "true" ]]; then
                    verbose "[DRY-RUN] 将删除: $file"
                else
                    if rm -f "$file"; then
                        verbose "删除: $file"
                        ((FILES_DELETED++))
                    fi
                fi
            done
        done
    done
    
    success "项目数据清理完成，共处理 $PROJECTS_CLEANED 个项目"
}

# 验证清理结果
verify_cleanup() {
    log "验证清理结果..."
    
    local remaining_files=0
    local config_paths
    readarray -t config_paths < <(get_config_paths)
    
    for config_path in "${config_paths[@]}"; do
        if [[ ! -d "$config_path" ]]; then
            continue
        fi
        
        while IFS= read -r -d '' file; do
            ((remaining_files++))
            verbose "残留文件: $file"
        done < <(find "$config_path" -name "*augment*" -type f -print0 2>/dev/null)
    done
    
    if [[ $remaining_files -eq 0 ]]; then
        success "清理验证通过！未发现残留文件"
    else
        warn "仍有 $remaining_files 个相关文件未清理"
        info "这可能是正常的（如插件本身的文件）"
    fi
}

# 生成清理报告
generate_report() {
    local end_time=$(date +%s)
    local duration=$((end_time - START_TIME))
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local report_file="$HOME/.augment_cleaner_report_$timestamp.txt"
    
    cat > "$report_file" << EOF
Augment插件清理报告 v$VERSION
==================

清理时间: $(date)
操作系统: $OSTYPE
用户: $USER
耗时: ${duration}秒

配置:
- 干运行模式: $DRY_RUN
- 详细模式: $VERBOSE
- 安全模式: $SAFE_MODE
- 选择IDE: ${SELECTED_IDE:-"全部"}
- 仅聊天记录: $CHAT_ONLY
- 保留设置: $KEEP_SETTINGS

清理统计:
- 删除文件: $FILES_DELETED
- 删除目录: $DIRS_DELETED
- 清理配置: $CONFIGS_CLEANED
- 清理缓存: $CACHES_CLEARED
- 清理项目: $PROJECTS_CLEANED

备份信息:
- 备份创建: $BACKUP_CREATED
- 备份位置: $BACKUP_PATH

发现的产品:
$(discover_installed_products | sed 's/^/- /')

建议:
1. 重启JetBrains IDE以使更改生效
2. 首次启动时插件会重新初始化
3. 如有问题可使用备份文件恢复

注意事项:
- 插件本身未被删除，只清理了跟踪数据
- 用户设置可能需要重新配置
- 如需完全移除插件，请在IDE中手动卸载
EOF
    
    success "清理报告已生成: $report_file"
}

# 交互式配置
interactive_config() {
    if [[ "$SILENT_MODE" == "true" ]]; then
        return 0
    fi
    
    echo -e "${PURPLE}=== Augment插件清理工具配置 ===${NC}"
    echo
    
    # 显示发现的产品
    local installed_products
    readarray -t installed_products < <(discover_installed_products)
    
    if [[ ${#installed_products[@]} -gt 0 ]]; then
        echo "发现的JetBrains产品:"
        local i=1
        for product in "${installed_products[@]}"; do
            local display_name="$product"
            for key in "${!JETBRAINS_PRODUCTS[@]}"; do
                if [[ "$product" == "$key"* ]]; then
                    display_name="${JETBRAINS_PRODUCTS[$key]}"
                    break
                fi
            done
            echo "  $i. $display_name ($product)"
            ((i++))
        done
        echo
    fi
    
    # 询问清理范围
    if [[ -z "$SELECTED_IDE" && "$CHAT_ONLY" != "true" ]]; then
        echo "选择清理范围:"
        echo "  1. 全部产品"
        echo "  2. 仅聊天记录"
        echo "  3. 指定IDE"
        read -p "请选择 (1-3) [1]: " choice
        
        case "$choice" in
            2)
                CHAT_ONLY=true
                info "✓ 将只清理聊天记录"
                ;;
            3)
                echo
                echo "可选的IDE:"
                for key in "${!JETBRAINS_PRODUCTS[@]}"; do
                    echo "  - $key (${JETBRAINS_PRODUCTS[$key]})"
                done
                read -p "请输入IDE名称: " ide_name
                SELECTED_IDE="$ide_name"
                info "✓ 将只清理 $SELECTED_IDE"
                ;;
            *)
                info "✓ 将清理所有JetBrains产品的Augment数据"
                ;;
        esac
        echo
    fi
    
    # 询问是否创建备份
    if [[ "$CREATE_BACKUP" == "true" ]]; then
        read -p "是否创建备份? (Y/n): " backup_choice
        if [[ "$backup_choice" =~ ^[Nn]$ ]]; then
            CREATE_BACKUP=false
            info "✓ 将不创建备份"
        else
            info "✓ 将创建备份"
        fi
        echo
    fi
    
    # 询问是否保留设置
    if [[ "$KEEP_SETTINGS" != "true" ]]; then
        read -p "是否保留用户设置? (y/N): " keep_choice
        if [[ "$keep_choice" =~ ^[Yy]$ ]]; then
            KEEP_SETTINGS=true
            info "✓ 将保留用户设置"
        fi
        echo
    fi
}

# 主函数
main() {
    # 解析命令行参数
    parse_args "$@"

    # 显示标题
    if [[ "$SILENT_MODE" != "true" ]]; then
        echo
        echo " █████╗ ██╗   ██╗ ██████╗ ███╗   ███╗███████╗███╗   ██╗████████╗"
        echo "██╔══██╗██║   ██║██╔════╝ ████╗ ████║██╔════╝████╗  ██║╚══██╔══╝"
        echo "███████║██║   ██║██║  ███╗██╔████╔██║█████╗  ██╔██╗ ██║   ██║   "
        echo "██╔══██║██║   ██║██║   ██║██║╚██╔╝██║██╔══╝  ██║╚██╗██║   ██║   "
        echo "██║  ██║╚██████╔╝╚██████╔╝██║ ╚═╝ ██║███████╗██║ ╚████║   ██║   "
        echo "╚═╝  ╚═╝ ╚═════╝  ╚═════╝ ╚═╝     ╚═╝╚══════╝╚═╝  ╚═══╝   ╚═╝   "
        echo
        echo "                     $SCRIPT_NAME v$VERSION"
        echo "                   支持全系列JetBrains产品"
        echo "                   关注公众号：煎饼果子卷AI"
        echo "========================================"
        echo
    fi
    
    # 检查环境
    check_ide_running
    
    # 交互式配置
    interactive_config
    
    # 显示警告和确认
    if [[ "$SILENT_MODE" != "true" && "$DRY_RUN" != "true" ]]; then
        warn "此工具将清理Augment插件的所有跟踪数据"
        warn "包括聊天记录、会话数据、配置文件等"
        echo
        
        read -p "确认继续? (y/N): " confirm
        if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
            echo "清理已取消"
            exit 0
        fi
        echo
    fi
    
    log "开始清理Augment插件数据..."
    
    # 创建备份
    create_backup
    
    # 执行清理
    clean_config_files
    clean_cache_files
    clean_project_data
    
    # 验证结果
    verify_cleanup
    
    # 生成报告
    generate_report
    
    # 显示结果摘要
    if [[ "$SILENT_MODE" != "true" ]]; then
        local end_time=$(date +%s)
        local duration=$((end_time - START_TIME))
        
        echo
        echo "=== 清理摘要 ==="
        echo "删除文件: $FILES_DELETED"
        echo "删除目录: $DIRS_DELETED"
        echo "清理配置: $CONFIGS_CLEANED"
        echo "清理缓存: $CACHES_CLEARED"
        echo "清理项目: $PROJECTS_CLEANED"
        
        if [[ "$BACKUP_CREATED" == "true" ]]; then
            echo "备份位置: $BACKUP_PATH"
        fi
        
        echo "耗时: ${duration}秒"
        
        echo
        echo "========================================"
        echo "██████╗ ██╗   ██╗██████╗ ██████╗ ████████╗ █████╗ ███╗   ██╗████████╗"
        echo "██╔══██╗██║   ██║██╔══██╗██╔══██╗╚══██╔══╝██╔══██╗████╗  ██║╚══██╔══╝"
        echo "██║  ██║██║   ██║██████╔╝██████╔╝   ██║   ███████║██╔██╗ ██║   ██║   "
        echo "██║  ██║██║   ██║██╔══██╗██╔══██╗   ██║   ██╔══██║██║╚██╗██║   ██║   "
        echo "██████╔╝╚██████╔╝██║  ██║██║  ██║   ██║   ██║  ██║██║ ╚████║   ██║   "
        echo "╚═════╝  ╚═════╝ ╚═╝  ╚═╝╚═╝  ╚═╝   ╚═╝   ╚═╝  ╚═╝╚═╝  ╚═══╝   ╚═╝   "
        echo
        echo "                            【重要】"
        echo -e "${RED}🔥🔥🔥 强烈建议手动删除项目里面 .idea，然后重新打开项目能避免二次风控！ 🔥🔥🔥${NC}"
        echo "                        关注公众号：煎饼果子卷AI"
        echo "========================================"
        echo
        echo "下一步:"
        echo "1. 重启JetBrains IDE"
        echo "2. Augment插件将以全新状态启动"
        echo "3. 如有问题，可使用备份文件恢复"

        if [[ "$DRY_RUN" == "true" ]]; then
            echo
            warn "注意: 这是干运行模式，实际上没有删除任何文件"
        fi
    fi
    
    success "Augment插件清理完成！"
}

# 运行主函数
main "$@"

