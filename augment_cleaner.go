package main

import (
	"archive/zip"
	"bufio"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"regexp"
	"runtime"
	"strings"
	"time"
)

// JetBrains产品定义
type JetBrainsProduct struct {
	Name        string
	DisplayName string
	ConfigDir   string
	Supported   bool
}

// 清理配置
type CleanerConfig struct {
	DryRun        bool
	Verbose       bool
	SafeMode      bool
	BackupEnabled bool
	SilentMode    bool
	SelectedIDE   string
	CleanChatOnly bool
	KeepSettings  bool
}

// 清理统计
type CleanStats struct {
	FilesDeleted    int
	DirsDeleted     int
	ConfigsCleaned  int
	CachesCleared   int
	ProjectsCleaned int
	BackupCreated   bool
	BackupPath      string
	StartTime       time.Time
	EndTime         time.Time
}

// 主清理器
type AugmentCleaner struct {
	Config    CleanerConfig
	Stats     CleanStats
	Products  []JetBrainsProduct
	BasePaths map[string]string
}

// JetBrains产品列表
var jetbrainsProducts = []JetBrainsProduct{
	{"IntelliJIdea", "IntelliJ IDEA", "IntelliJIdea", true},
	{"PyCharm", "PyCharm", "PyCharm", true},
	{"WebStorm", "WebStorm", "WebStorm", true},
	{"PhpStorm", "PhpStorm", "PhpStorm", true},
	{"RubyMine", "RubyMine", "RubyMine", true},
	{"CLion", "CLion", "CLion", true},
	{"DataGrip", "DataGrip", "DataGrip", true},
	{"GoLand", "GoLand", "GoLand", true},
	{"Rider", "Rider", "Rider", true},
	{"AppCode", "AppCode", "AppCode", false}, // 已停止开发
	{"AndroidStudio", "Android Studio", "AndroidStudio", true},
	{"Fleet", "Fleet", "Fleet", false}, // 架构不同
}

// Augment相关的配置属性模式
var augmentPatterns = []string{
	"augment\\.",
	"Augment\\.",
	"AUGMENT_",
	"sessionId",
	"SessionId",
	"installationId",
	"chat\\.history",
	"conversation\\.",
	"feedback\\.",
	"memory\\.",
	"context\\.",
	"preferences\\.augment",
}

// 需要清理的文件模式
var cleanupPatterns = []string{
	"*augment*",
	"*Augment*",
	"*session*",
	"*Session*",
	"chat_history.*",
	"conversations.*",
	"augment.xml",
	"augment.log",
	"*.augment.cache",
}

func NewAugmentCleaner() *AugmentCleaner {
	cleaner := &AugmentCleaner{
		Config: CleanerConfig{
			SafeMode:      true,
			BackupEnabled: true,
			Verbose:       false,
			DryRun:        false,
			SilentMode:    false,
			CleanChatOnly: false,
			KeepSettings:  false,
		},
		Stats: CleanStats{
			StartTime: time.Now(),
		},
		Products:  jetbrainsProducts,
		BasePaths: make(map[string]string),
	}

	cleaner.initBasePaths()
	return cleaner
}

func (c *AugmentCleaner) initBasePaths() {
	homeDir, _ := os.UserHomeDir()

	switch runtime.GOOS {
	case "windows":
		appData := os.Getenv("APPDATA")
		localAppData := os.Getenv("LOCALAPPDATA")
		if appData == "" {
			appData = filepath.Join(homeDir, "AppData", "Roaming")
		}
		if localAppData == "" {
			localAppData = filepath.Join(homeDir, "AppData", "Local")
		}
		c.BasePaths["config"] = filepath.Join(appData, "JetBrains")
		c.BasePaths["cache"] = filepath.Join(localAppData, "JetBrains")
		c.BasePaths["logs"] = filepath.Join(localAppData, "JetBrains")
	case "darwin":
		c.BasePaths["config"] = filepath.Join(homeDir, "Library", "Application Support", "JetBrains")
		c.BasePaths["cache"] = filepath.Join(homeDir, "Library", "Caches", "JetBrains")
		c.BasePaths["logs"] = filepath.Join(homeDir, "Library", "Logs", "JetBrains")
		c.BasePaths["preferences"] = filepath.Join(homeDir, "Library", "Preferences", "JetBrains")
	default: // Linux
		c.BasePaths["config"] = filepath.Join(homeDir, ".config", "JetBrains")
		c.BasePaths["cache"] = filepath.Join(homeDir, ".cache", "JetBrains")
		c.BasePaths["share"] = filepath.Join(homeDir, ".local", "share", "JetBrains")
	}
}

func (c *AugmentCleaner) log(message string) {
	if !c.Config.SilentMode {
		timestamp := time.Now().Format("15:04:05")
		fmt.Printf("[%s] %s\n", timestamp, message)
	}
}

func (c *AugmentCleaner) verbose(message string) {
	if c.Config.Verbose && !c.Config.SilentMode {
		fmt.Printf("  → %s\n", message)
	}
}

func (c *AugmentCleaner) warn(message string) {
	if !c.Config.SilentMode {
		fmt.Printf("⚠️  %s\n", message)
	}
}

func (c *AugmentCleaner) success(message string) {
	if !c.Config.SilentMode {
		fmt.Printf("✅ %s\n", message)
	}
}

func (c *AugmentCleaner) error(message string) {
	fmt.Printf("❌ %s\n", message)
}

// 检查IDE是否正在运行
func (c *AugmentCleaner) checkIDERunning() error {
	c.log("检查JetBrains IDE是否正在运行...")

	processes := []string{
		"idea64.exe", "idea.exe", "intellij",
		"pycharm64.exe", "pycharm.exe", "pycharm",
		"webstorm64.exe", "webstorm.exe", "webstorm",
		"phpstorm64.exe", "phpstorm.exe", "phpstorm",
		"rubymine64.exe", "rubymine.exe", "rubymine",
		"clion64.exe", "clion.exe", "clion",
		"datagrip64.exe", "datagrip.exe", "datagrip",
		"goland64.exe", "goland.exe", "goland",
		"rider64.exe", "rider.exe", "rider",
		"studio64.exe", "studio.exe", "android-studio",
		"fleet.exe", "fleet",
	}

	for _, proc := range processes {
		if c.isProcessRunning(proc) {
			return fmt.Errorf("检测到 %s 正在运行，请先关闭所有JetBrains IDE", proc)
		}
	}

	c.success("未检测到运行中的JetBrains IDE")
	return nil
}

func (c *AugmentCleaner) isProcessRunning(processName string) bool {
	// 简化的进程检查，实际实现需要根据操作系统调用相应的API
	// 这里只是示例
	return false
}

// 发现已安装的JetBrains产品
func (c *AugmentCleaner) discoverInstalledProducts() []JetBrainsProduct {
	c.log("扫描已安装的JetBrains产品...")

	var installedProducts []JetBrainsProduct

	for _, basePath := range c.BasePaths {
		if _, err := os.Stat(basePath); os.IsNotExist(err) {
			continue
		}

		entries, err := os.ReadDir(basePath)
		if err != nil {
			continue
		}

		for _, entry := range entries {
			if !entry.IsDir() {
				continue
			}

			dirName := entry.Name()
			for _, product := range c.Products {
				if strings.HasPrefix(dirName, product.ConfigDir) && product.Supported {
					// 检查是否已经添加过
					found := false
					for _, installed := range installedProducts {
						if installed.ConfigDir == dirName {
							found = true
							break
						}
					}
					if !found {
						installedProduct := product
						installedProduct.ConfigDir = dirName
						installedProducts = append(installedProducts, installedProduct)
						c.verbose(fmt.Sprintf("发现: %s (%s)", product.DisplayName, dirName))
					}
				}
			}
		}
	}

	c.log(fmt.Sprintf("发现 %d 个已安装的JetBrains产品", len(installedProducts)))
	return installedProducts
}

// 创建备份
func (c *AugmentCleaner) createBackup() error {
	if !c.Config.BackupEnabled {
		return nil
	}

	c.log("创建备份...")

	homeDir, _ := os.UserHomeDir()
	timestamp := time.Now().Format("20060102_150405")
	backupDir := filepath.Join(homeDir, ".augment_cleaner_backup", timestamp)

	if err := os.MkdirAll(backupDir, 0755); err != nil {
		return fmt.Errorf("创建备份目录失败: %v", err)
	}

	// 备份配置文件
	for pathType, basePath := range c.BasePaths {
		if _, err := os.Stat(basePath); os.IsNotExist(err) {
			continue
		}

		backupPath := filepath.Join(backupDir, pathType)
		if err := c.copyDir(basePath, backupPath); err != nil {
			c.warn(fmt.Sprintf("备份 %s 失败: %v", pathType, err))
		} else {
			c.verbose(fmt.Sprintf("已备份: %s", pathType))
		}
	}

	// 创建备份信息文件
	backupInfo := map[string]interface{}{
		"timestamp": timestamp,
		"os":        runtime.GOOS,
		"user":      os.Getenv("USER"),
		"products":  c.discoverInstalledProducts(),
		"config":    c.Config,
	}

	infoFile := filepath.Join(backupDir, "backup_info.json")
	if data, err := json.MarshalIndent(backupInfo, "", "  "); err == nil {
		os.WriteFile(infoFile, data, 0644)
	}

	// 压缩备份
	zipPath := backupDir + ".zip"
	if err := c.zipDir(backupDir, zipPath); err == nil {
		os.RemoveAll(backupDir) // 删除未压缩的目录
		c.Stats.BackupCreated = true
		c.Stats.BackupPath = zipPath
		c.success(fmt.Sprintf("备份创建完成: %s", zipPath))
	} else {
		c.warn(fmt.Sprintf("备份压缩失败: %v", err))
		c.Stats.BackupPath = backupDir
	}

	return nil
}

// 清理配置文件
func (c *AugmentCleaner) cleanConfigFiles() error {
	c.log("清理配置文件...")

	installedProducts := c.discoverInstalledProducts()

	for _, product := range installedProducts {
		if c.Config.SelectedIDE != "" && !strings.Contains(product.ConfigDir, c.Config.SelectedIDE) {
			continue
		}

		c.verbose(fmt.Sprintf("清理 %s 配置...", product.DisplayName))

		for pathType, basePath := range c.BasePaths {
			productPath := filepath.Join(basePath, product.ConfigDir)
			if _, err := os.Stat(productPath); os.IsNotExist(err) {
				continue
			}

			// 清理options目录
			optionsPath := filepath.Join(productPath, "options")
			if err := c.cleanOptionsDir(optionsPath); err != nil {
				c.warn(fmt.Sprintf("清理 %s options 失败: %v", pathType, err))
			}

			// 清理插件目录
			pluginsPath := filepath.Join(productPath, "plugins", "intellij-augment")
			if err := c.cleanPluginDir(pluginsPath); err != nil {
				c.verbose(fmt.Sprintf("清理 %s 插件目录失败: %v", pathType, err))
			}

			// 清理其他Augment文件
			if err := c.cleanAugmentFiles(productPath); err != nil {
				c.warn(fmt.Sprintf("清理 %s Augment文件失败: %v", pathType, err))
			}
		}

		c.Stats.ConfigsCleaned++
	}

	c.success(fmt.Sprintf("配置文件清理完成，共处理 %d 个产品", c.Stats.ConfigsCleaned))
	return nil
}

// 清理options目录
func (c *AugmentCleaner) cleanOptionsDir(optionsPath string) error {
	if _, err := os.Stat(optionsPath); os.IsNotExist(err) {
		return nil
	}

	configFiles := []string{"other.xml", "project.default.xml", "ide.general.xml", "editor.xml"}

	for _, configFile := range configFiles {
		filePath := filepath.Join(optionsPath, configFile)
		if err := c.cleanXMLFile(filePath); err != nil {
			c.verbose(fmt.Sprintf("清理 %s 失败: %v", configFile, err))
		}
	}

	// 删除augment.xml
	augmentXML := filepath.Join(optionsPath, "augment.xml")
	if c.Config.DryRun {
		if _, err := os.Stat(augmentXML); err == nil {
			c.verbose(fmt.Sprintf("[DRY-RUN] 将删除: %s", augmentXML))
		}
	} else {
		if err := os.Remove(augmentXML); err == nil {
			c.verbose(fmt.Sprintf("删除: %s", augmentXML))
			c.Stats.FilesDeleted++
		}
	}

	return nil
}

// 清理XML配置文件中的Augment属性
func (c *AugmentCleaner) cleanXMLFile(filePath string) error {
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil
	}

	content, err := os.ReadFile(filePath)
	if err != nil {
		return err
	}

	originalContent := string(content)
	modifiedContent := originalContent

	// 移除Augment相关的属性
	for _, pattern := range augmentPatterns {
		re := regexp.MustCompile(fmt.Sprintf(`\s*<property name="%s[^"]*"[^>]*/>`, pattern))
		modifiedContent = re.ReplaceAllString(modifiedContent, "")
	}

	// 如果内容有变化且不是干运行模式
	if modifiedContent != originalContent {
		if c.Config.DryRun {
			c.verbose(fmt.Sprintf("[DRY-RUN] 将清理: %s", filePath))
		} else {
			if err := os.WriteFile(filePath, []byte(modifiedContent), 0644); err != nil {
				return err
			}
			c.verbose(fmt.Sprintf("清理: %s", filePath))
			c.Stats.FilesDeleted++
		}
	}

	return nil
}

// 清理插件目录
func (c *AugmentCleaner) cleanPluginDir(pluginPath string) error {
	if _, err := os.Stat(pluginPath); os.IsNotExist(err) {
		return nil
	}

	// 清理数据目录
	dataDirs := []string{"data", "cache", "logs", "tmp"}
	for _, dir := range dataDirs {
		dirPath := filepath.Join(pluginPath, dir)
		if c.Config.DryRun {
			if _, err := os.Stat(dirPath); err == nil {
				c.verbose(fmt.Sprintf("[DRY-RUN] 将删除目录: %s", dirPath))
			}
		} else {
			if err := os.RemoveAll(dirPath); err == nil {
				c.verbose(fmt.Sprintf("删除目录: %s", dirPath))
				c.Stats.DirsDeleted++
			}
		}
	}

	// 清理特定文件
	return filepath.Walk(pluginPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil
		}

		if info.IsDir() {
			return nil
		}

		fileName := info.Name()
		for _, pattern := range cleanupPatterns {
			if matched, _ := filepath.Match(pattern, fileName); matched {
				if c.Config.DryRun {
					c.verbose(fmt.Sprintf("[DRY-RUN] 将删除: %s", path))
				} else {
					if err := os.Remove(path); err == nil {
						c.verbose(fmt.Sprintf("删除: %s", path))
						c.Stats.FilesDeleted++
					}
				}
				break
			}
		}

		return nil
	})
}

// 清理其他Augment文件
func (c *AugmentCleaner) cleanAugmentFiles(productPath string) error {
	return filepath.Walk(productPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil
		}

		if info.IsDir() {
			return nil
		}

		fileName := strings.ToLower(info.Name())
		if strings.Contains(fileName, "augment") || strings.Contains(fileName, "session") {
			if c.Config.DryRun {
				c.verbose(fmt.Sprintf("[DRY-RUN] 将删除: %s", path))
			} else {
				if err := os.Remove(path); err == nil {
					c.verbose(fmt.Sprintf("删除: %s", path))
					c.Stats.FilesDeleted++
				}
			}
		}

		return nil
	})
}

// 清理项目级数据
func (c *AugmentCleaner) cleanProjectData() error {
	c.log("清理项目级数据...")

	homeDir, _ := os.UserHomeDir()
	projectDirs := []string{
		filepath.Join(homeDir, "Projects"),
		filepath.Join(homeDir, "workspace"),
		filepath.Join(homeDir, "dev"),
		filepath.Join(homeDir, "code"),
		filepath.Join(homeDir, "Documents"),
		filepath.Join(homeDir, "Desktop"),
	}

	for _, projectDir := range projectDirs {
		if _, err := os.Stat(projectDir); os.IsNotExist(err) {
			continue
		}

		err := filepath.Walk(projectDir, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return nil
			}

			if info.IsDir() && info.Name() == ".idea" {
				augmentDir := filepath.Join(path, "augment")
				if c.Config.DryRun {
					if _, err := os.Stat(augmentDir); err == nil {
						c.verbose(fmt.Sprintf("[DRY-RUN] 将删除项目数据: %s", augmentDir))
					}
				} else {
					if err := os.RemoveAll(augmentDir); err == nil {
						c.verbose(fmt.Sprintf("删除项目数据: %s", augmentDir))
						c.Stats.ProjectsCleaned++
					}
				}

				// 清理.idea目录中的Augment文件
				filepath.Walk(path, func(subPath string, subInfo os.FileInfo, subErr error) error {
					if subErr != nil || subInfo.IsDir() {
						return nil
					}

					fileName := strings.ToLower(subInfo.Name())
					if strings.Contains(fileName, "augment") {
						if c.Config.DryRun {
							c.verbose(fmt.Sprintf("[DRY-RUN] 将删除: %s", subPath))
						} else {
							if err := os.Remove(subPath); err == nil {
								c.verbose(fmt.Sprintf("删除: %s", subPath))
								c.Stats.FilesDeleted++
							}
						}
					}
					return nil
				})
			}

			return nil
		})

		if err != nil {
			c.warn(fmt.Sprintf("扫描项目目录 %s 失败: %v", projectDir, err))
		}
	}

	c.success(fmt.Sprintf("项目数据清理完成，共处理 %d 个项目", c.Stats.ProjectsCleaned))
	return nil
}

// 清理缓存文件
func (c *AugmentCleaner) cleanCacheFiles() error {
	c.log("清理缓存文件...")

	cachePaths := []string{c.BasePaths["cache"]}
	if logsPath, exists := c.BasePaths["logs"]; exists {
		cachePaths = append(cachePaths, logsPath)
	}

	for _, cachePath := range cachePaths {
		if _, err := os.Stat(cachePath); os.IsNotExist(err) {
			continue
		}

		err := filepath.Walk(cachePath, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return nil
			}

			fileName := strings.ToLower(info.Name())
			if strings.Contains(fileName, "augment") {
				if c.Config.DryRun {
					c.verbose(fmt.Sprintf("[DRY-RUN] 将删除缓存: %s", path))
				} else {
					if info.IsDir() {
						if err := os.RemoveAll(path); err == nil {
							c.verbose(fmt.Sprintf("删除缓存目录: %s", path))
							c.Stats.DirsDeleted++
						}
					} else {
						if err := os.Remove(path); err == nil {
							c.verbose(fmt.Sprintf("删除缓存文件: %s", path))
							c.Stats.FilesDeleted++
						}
					}
				}
			}

			return nil
		})

		if err != nil {
			c.warn(fmt.Sprintf("清理缓存目录 %s 失败: %v", cachePath, err))
		}

		c.Stats.CachesCleared++
	}

	c.success("缓存文件清理完成")
	return nil
}

// 验证清理结果
func (c *AugmentCleaner) verifyCleanup() error {
	c.log("验证清理结果...")

	remainingFiles := 0

	for _, basePath := range c.BasePaths {
		if _, err := os.Stat(basePath); os.IsNotExist(err) {
			continue
		}

		filepath.Walk(basePath, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return nil
			}

			fileName := strings.ToLower(info.Name())
			if strings.Contains(fileName, "augment") && !info.IsDir() {
				remainingFiles++
				c.verbose(fmt.Sprintf("残留文件: %s", path))
			}

			return nil
		})
	}

	if remainingFiles == 0 {
		c.success("清理验证通过！未发现残留文件")
	} else {
		c.warn(fmt.Sprintf("仍有 %d 个相关文件未清理", remainingFiles))
	}

	return nil
}

// 生成清理报告
func (c *AugmentCleaner) generateReport() error {
	c.Stats.EndTime = time.Now()
	duration := c.Stats.EndTime.Sub(c.Stats.StartTime)

	homeDir, _ := os.UserHomeDir()
	timestamp := time.Now().Format("20060102_150405")
	reportFile := filepath.Join(homeDir, fmt.Sprintf(".augment_cleaner_report_%s.json", timestamp))

	report := map[string]interface{}{
		"timestamp":      timestamp,
		"duration":       duration.String(),
		"config":         c.Config,
		"stats":          c.Stats,
		"os":             runtime.GOOS,
		"products_found": c.discoverInstalledProducts(),
	}

	data, err := json.MarshalIndent(report, "", "  ")
	if err != nil {
		return err
	}

	if err := os.WriteFile(reportFile, data, 0644); err != nil {
		return err
	}

	c.success(fmt.Sprintf("清理报告已生成: %s", reportFile))
	return nil
}

// 辅助函数：复制目录
func (c *AugmentCleaner) copyDir(src, dst string) error {
	return filepath.Walk(src, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		relPath, err := filepath.Rel(src, path)
		if err != nil {
			return err
		}

		dstPath := filepath.Join(dst, relPath)

		if info.IsDir() {
			return os.MkdirAll(dstPath, info.Mode())
		}

		return c.copyFile(path, dstPath)
	})
}

// 辅助函数：复制文件
func (c *AugmentCleaner) copyFile(src, dst string) error {
	srcFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer srcFile.Close()

	if err := os.MkdirAll(filepath.Dir(dst), 0755); err != nil {
		return err
	}

	dstFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer dstFile.Close()

	_, err = io.Copy(dstFile, srcFile)
	return err
}

// 辅助函数：压缩目录
func (c *AugmentCleaner) zipDir(src, dst string) error {
	zipFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer zipFile.Close()

	zipWriter := zip.NewWriter(zipFile)
	defer zipWriter.Close()

	return filepath.Walk(src, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() {
			return nil
		}

		relPath, err := filepath.Rel(src, path)
		if err != nil {
			return err
		}

		zipEntry, err := zipWriter.Create(relPath)
		if err != nil {
			return err
		}

		file, err := os.Open(path)
		if err != nil {
			return err
		}
		defer file.Close()

		_, err = io.Copy(zipEntry, file)
		return err
	})
}

// 主清理函数
func (c *AugmentCleaner) Clean() error {
	c.log("开始Augment插件清理...")

	// 检查IDE运行状态
	if err := c.checkIDERunning(); err != nil {
		return err
	}

	// 创建备份
	if err := c.createBackup(); err != nil {
		c.warn(fmt.Sprintf("备份创建失败: %v", err))
	}

	// 执行清理
	if err := c.cleanConfigFiles(); err != nil {
		return fmt.Errorf("配置文件清理失败: %v", err)
	}

	if err := c.cleanCacheFiles(); err != nil {
		c.warn(fmt.Sprintf("缓存清理失败: %v", err))
	}

	if err := c.cleanProjectData(); err != nil {
		c.warn(fmt.Sprintf("项目数据清理失败: %v", err))
	}

	// 验证结果
	if err := c.verifyCleanup(); err != nil {
		c.warn(fmt.Sprintf("清理验证失败: %v", err))
	}

	// 生成报告
	if err := c.generateReport(); err != nil {
		c.warn(fmt.Sprintf("报告生成失败: %v", err))
	}

	c.success("Augment插件清理完成！")
	return nil
}

// 显示帮助信息
func showHelp() {
	fmt.Println("Augment插件清理工具 v2.0")
	fmt.Println("支持全系列JetBrains产品的Augment插件数据清理")
	fmt.Println()
	fmt.Println("用法:")
	fmt.Println("  augment-cleaner [选项]")
	fmt.Println()
	fmt.Println("选项:")
	fmt.Println("  -h, --help          显示帮助信息")
	fmt.Println("  -v, --verbose       详细输出模式")
	fmt.Println("  -d, --dry-run       干运行模式（只显示将要清理的文件）")
	fmt.Println("  -s, --silent        静默模式（无交互）")
	fmt.Println("  --no-backup         禁用备份")
	fmt.Println("  --unsafe            非安全模式（更彻底的清理）")
	fmt.Println("  --ide=NAME          只清理指定的IDE（如：IntelliJIdea）")
	fmt.Println("  --chat-only         只清理聊天记录")
	fmt.Println("  --keep-settings     保留用户设置")
	fmt.Println()
	fmt.Println("支持的IDE:")
	for _, product := range jetbrainsProducts {
		if product.Supported {
			fmt.Printf("  - %s\n", product.DisplayName)
		}
	}
	fmt.Println()
	fmt.Println("示例:")
	fmt.Println("  augment-cleaner                    # 默认清理")
	fmt.Println("  augment-cleaner -d                 # 干运行模式")
	fmt.Println("  augment-cleaner --ide=PyCharm      # 只清理PyCharm")
	fmt.Println("  augment-cleaner --chat-only        # 只清理聊天记录")
}

// 交互式配置
func (c *AugmentCleaner) interactiveConfig() error {
	if c.Config.SilentMode {
		return nil
	}

	reader := bufio.NewReader(os.Stdin)

	fmt.Println("=== Augment插件清理工具配置 ===")
	fmt.Println()

	// 显示发现的产品
	products := c.discoverInstalledProducts()
	if len(products) > 0 {
		fmt.Println("发现的JetBrains产品:")
		for i, product := range products {
			fmt.Printf("  %d. %s (%s)\n", i+1, product.DisplayName, product.ConfigDir)
		}
		fmt.Println()
	}

	// 询问清理范围
	fmt.Print("选择清理范围 (1=全部, 2=仅聊天记录, 3=指定IDE): ")
	choice, _ := reader.ReadString('\n')
	choice = strings.TrimSpace(choice)

	switch choice {
	case "2":
		c.Config.CleanChatOnly = true
		fmt.Println("✓ 将只清理聊天记录")
	case "3":
		fmt.Print("请输入IDE名称 (如: IntelliJIdea, PyCharm): ")
		ide, _ := reader.ReadString('\n')
		c.Config.SelectedIDE = strings.TrimSpace(ide)
		fmt.Printf("✓ 将只清理 %s\n", c.Config.SelectedIDE)
	default:
		fmt.Println("✓ 将清理所有JetBrains产品的Augment数据")
	}

	// 询问是否创建备份 (默认为是)
	fmt.Print("是否创建备份? (Y/n) [默认: Y]: ")
	backup, _ := reader.ReadString('\n')
	backup = strings.TrimSpace(strings.ToLower(backup))
	if backup == "n" || backup == "no" {
		c.Config.BackupEnabled = false
		fmt.Println("✓ 将不创建备份")
	} else {
		// 默认创建备份，包括直接回车的情况
		c.Config.BackupEnabled = true
		fmt.Println("✓ 将创建备份")
	}

	// 询问是否保留设置
	fmt.Print("是否保留用户设置? (y/N): ")
	keep, _ := reader.ReadString('\n')
	keep = strings.TrimSpace(strings.ToLower(keep))
	if keep == "y" || keep == "yes" {
		c.Config.KeepSettings = true
		fmt.Println("✓ 将保留用户设置")
	}

	fmt.Println()
	return nil
}

func main() {
	// 显示Logo和提示语
	fmt.Println(`
	    █████╗ ██╗   ██╗ ██████╗ ███╗   ███╗███████╗███╗   ██╗████████╗
	   ██╔══██╗██║   ██║██╔════╝ ████╗ ████║██╔════╝████╗  ██║╚══██╔══╝
	   ███████║██║   ██║██║  ███╗██╔████╔██║█████╗  ██╔██╗ ██║   ██║
	   ██╔══██║██║   ██║██║   ██║██║╚██╔╝██║██╔══╝  ██║╚██╗██║   ██║
	   ██║  ██║╚██████╔╝╚██████╔╝██║ ╚═╝ ██║███████╗██║ ╚████║   ██║
	   ╚═╝  ╚═╝ ╚═════╝  ╚═════╝ ╚═╝     ╚═╝╚══════╝╚═╝  ╚═══╝   ╚═╝
	   
						   Augment IDEA清理工具
						 关注公众号：煎饼果子卷AI
	   `)

	fmt.Println("Augment插件清理工具 v2.0")
	fmt.Println("========================================")
	cleaner := NewAugmentCleaner()

	// 解析命令行参数
	for i, arg := range os.Args[1:] {
		switch arg {
		case "-h", "--help":
			showHelp()
			return
		case "-v", "--verbose":
			cleaner.Config.Verbose = true
		case "-d", "--dry-run":
			cleaner.Config.DryRun = true
		case "-s", "--silent":
			cleaner.Config.SilentMode = true
		case "--no-backup":
			cleaner.Config.BackupEnabled = false
		case "--unsafe":
			cleaner.Config.SafeMode = false
		case "--chat-only":
			cleaner.Config.CleanChatOnly = true
		case "--keep-settings":
			cleaner.Config.KeepSettings = true
		default:
			if strings.HasPrefix(arg, "--ide=") {
				cleaner.Config.SelectedIDE = strings.TrimPrefix(arg, "--ide=")
			}
		}
		_ = i
	}

	// 显示标题
	if !cleaner.Config.SilentMode {
		fmt.Println("========================================")
		fmt.Println("    Augment插件清理工具 v2.0")
		fmt.Println("  支持全系列JetBrains产品")
		fmt.Println("========================================")
		fmt.Println()
	}

	// 交互式配置
	if err := cleaner.interactiveConfig(); err != nil {
		cleaner.error(fmt.Sprintf("配置失败: %v", err))
		os.Exit(1)
	}

	// 显示配置摘要
	if !cleaner.Config.SilentMode && !cleaner.Config.DryRun {
		fmt.Println("⚠️  警告: 此操作将清理Augment插件的所有跟踪数据")
		fmt.Println("⚠️  包括聊天记录、会话数据、配置文件等")
		fmt.Println()

		reader := bufio.NewReader(os.Stdin)
		fmt.Print("确认继续? (y/N): ")
		confirm, _ := reader.ReadString('\n')
		confirm = strings.TrimSpace(strings.ToLower(confirm))

		if confirm != "y" && confirm != "yes" {
			fmt.Println("清理已取消")
			return
		}
		fmt.Println()
	}

	// 执行清理
	if err := cleaner.Clean(); err != nil {
		cleaner.error(fmt.Sprintf("清理失败: %v", err))
		os.Exit(1)
	}

	// 显示结果摘要
	if !cleaner.Config.SilentMode {
		fmt.Println()
		fmt.Println("=== 清理摘要 ===")
		fmt.Printf("删除文件: %d\n", cleaner.Stats.FilesDeleted)
		fmt.Printf("删除目录: %d\n", cleaner.Stats.DirsDeleted)
		fmt.Printf("清理配置: %d\n", cleaner.Stats.ConfigsCleaned)
		fmt.Printf("清理缓存: %d\n", cleaner.Stats.CachesCleared)
		fmt.Printf("清理项目: %d\n", cleaner.Stats.ProjectsCleaned)

		if cleaner.Stats.BackupCreated {
			fmt.Printf("备份位置: %s\n", cleaner.Stats.BackupPath)
		}

		duration := cleaner.Stats.EndTime.Sub(cleaner.Stats.StartTime)
		fmt.Printf("耗时: %s\n", duration.String())

		fmt.Println()
		fmt.Println("========================================")
		fmt.Println("██████╗ ██╗   ██╗██████╗ ██████╗ ████████╗ █████╗ ███╗   ██╗████████╗")
		fmt.Println("██╔══██╗██║   ██║██╔══██╗██╔══██╗╚══██╔══╝██╔══██╗████╗  ██║╚══██╔══╝")
		fmt.Println("██║  ██║██║   ██║██████╔╝██████╔╝   ██║   ███████║██╔██╗ ██║   ██║   ")
		fmt.Println("██║  ██║██║   ██║██╔══██╗██╔══██╗   ██║   ██╔══██║██║╚██╗██║   ██║   ")
		fmt.Println("██████╔╝╚██████╔╝██║  ██║██║  ██║   ██║   ██║  ██║██║ ╚████║   ██║   ")
		fmt.Println("╚═════╝  ╚═════╝ ╚═╝  ╚═╝╚═╝  ╚═╝   ╚═╝   ╚═╝  ╚═╝╚═╝  ╚═══╝   ╚═╝   ")
		fmt.Println()
		fmt.Println("                            【重要】")
		fmt.Println("🔥🔥🔥 强烈建议手动删除项目里面 .idea，然后重新打开项目能避免二次风控！ 🔥🔥🔥")
		fmt.Println("                        关注公众号：煎饼果子卷AI")
		fmt.Println("========================================")
		fmt.Println()
		fmt.Println("下一步:")
		fmt.Println("1. 重启JetBrains IDE")
		fmt.Println("2. Augment插件将以全新状态启动")
		fmt.Println("3. 如有问题，可使用备份文件恢复")

		if cleaner.Config.DryRun {
			fmt.Println()
			fmt.Println("注意: 这是干运行模式，实际上没有删除任何文件")
		}
	}
}
