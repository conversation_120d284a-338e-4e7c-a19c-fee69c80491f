# Augment清理工具构建脚本 - PowerShell版本

Write-Host "🧹 Augment清理工具 - 跨平台构建" -ForegroundColor Cyan
Write-Host "==================================" -ForegroundColor Cyan

# 版本管理
if (-not (Test-Path "version.txt")) {
    "2.0" | Out-File -FilePath "version.txt" -Encoding UTF8
    Write-Host "📝 创建初始版本文件: 2.0" -ForegroundColor Yellow
}

# 读取当前版本
$currentVersion = Get-Content "version.txt" -Raw
$currentVersion = $currentVersion.Trim()
Write-Host "📋 当前版本: $currentVersion" -ForegroundColor Cyan

# 计算新版本 (增加0.1)
$versionParts = $currentVersion.Split('.')
$major = [int]$versionParts[0]
$minor = [int]$versionParts[1]
$newMinor = $minor + 1
$newVersion = "$major.$newMinor"

Write-Host "🔄 更新版本: $currentVersion → $newVersion" -ForegroundColor Yellow
$newVersion | Out-File -FilePath "version.txt" -Encoding UTF8

# 检查Go环境
$goVersion = Get-Command go -ErrorAction SilentlyContinue
if (-not $goVersion) {
    Write-Host "❌ 未找到Go编译环境" -ForegroundColor Red
    Write-Host "请先安装Go: https://golang.org/dl/" -ForegroundColor Yellow
    exit 1
}

$goVersionOutput = go version
Write-Host "✅ 检测到Go编译环境: $goVersionOutput" -ForegroundColor Green

# 创建构建目录
if (-not (Test-Path "dist")) {
    New-Item -ItemType Directory -Path "dist" | Out-Null
}

Write-Host "📦 开始编译清理工具 v$newVersion..." -ForegroundColor Yellow

# 构建目标平台
$platforms = @(
    # AMD64架构 (x86_64)
    @{OS = "windows"; ARCH = "amd64"},
    @{OS = "linux"; ARCH = "amd64"},
    @{OS = "darwin"; ARCH = "amd64"},
    
    # ARM64架构 (现代ARM设备)
    @{OS = "windows"; ARCH = "arm64"},
    @{OS = "linux"; ARCH = "arm64"},
    @{OS = "darwin"; ARCH = "arm64"},
    
    # ARM32架构 (嵌入式设备)
    @{OS = "linux"; ARCH = "arm"}
)

# 按架构分组构建
$archGroups = @{
    "amd64" = @{Name = "AMD64架构 (x86_64)"; Platforms = @()}
    "arm64" = @{Name = "ARM64架构 (现代ARM设备)"; Platforms = @()}
    "arm" = @{Name = "ARM32架构 (嵌入式设备)"; Platforms = @()}
}

# 将平台按架构分组
foreach ($platform in $platforms) {
    $archGroups[$platform.ARCH].Platforms += $platform
}

# 按组构建
foreach ($archType in @("amd64", "arm64", "arm")) {
    $group = $archGroups[$archType]
    if ($group.Platforms.Count -gt 0) {
        Write-Host ""
        Write-Host "🏗️ $($group.Name)" -ForegroundColor Magenta
        
        foreach ($platform in $group.Platforms) {
            $os = $platform.OS
            $arch = $platform.ARCH
            
            Write-Host "构建 $os/$arch..." -ForegroundColor White

            $outputName = "augment-cleaner-$os-$arch-v$newVersion"
            if ($os -eq "windows") {
                $outputName += ".exe"
            }
            
            # 设置环境变量并编译
            $env:GOOS = $os
            $env:GOARCH = $arch
            
            $buildResult = Start-Process -FilePath "go" -ArgumentList "build", "-o", "dist/$outputName", "augment_cleaner.go" -Wait -PassThru -NoNewWindow
            
            if ($buildResult.ExitCode -eq 0) {
                Write-Host "✅ $outputName" -ForegroundColor Green
            } else {
                Write-Host "❌ 构建失败: $outputName" -ForegroundColor Red
            }
        }
    }
}

Write-Host ""
Write-Host "📁 构建结果:" -ForegroundColor Cyan
Get-ChildItem -Path "dist" | Format-Table Name, Length, LastWriteTime

Write-Host ""
Write-Host "🎉 构建完成！版本: v$newVersion" -ForegroundColor Green
Write-Host ""
Write-Host "使用方法:" -ForegroundColor Yellow
Write-Host ""
Write-Host "AMD64架构 (x86_64):" -ForegroundColor Cyan
Write-Host "  Windows: dist\augment-cleaner-windows-amd64-v$newVersion.exe" -ForegroundColor White
Write-Host "  Linux:   dist\augment-cleaner-linux-amd64-v$newVersion" -ForegroundColor White
Write-Host "  macOS:   dist\augment-cleaner-darwin-amd64-v$newVersion" -ForegroundColor White
Write-Host ""
Write-Host "ARM64架构 (Apple Silicon, ARM服务器):" -ForegroundColor Cyan
Write-Host "  Windows: dist\augment-cleaner-windows-arm64-v$newVersion.exe" -ForegroundColor White
Write-Host "  Linux:   dist\augment-cleaner-linux-arm64-v$newVersion" -ForegroundColor White
Write-Host "  macOS:   dist\augment-cleaner-darwin-arm64-v$newVersion" -ForegroundColor White
Write-Host ""
Write-Host "ARM32架构 (树莓派等嵌入式设备):" -ForegroundColor Cyan
Write-Host "  Linux:   dist\augment-cleaner-linux-arm-v$newVersion" -ForegroundColor White
Write-Host ""
Write-Host "提示: 可以直接双击exe文件运行Windows版本" -ForegroundColor Yellow
Write-Host ""
Write-Host "⚠️  重要提示 - Unix系统权限设置:" -ForegroundColor Red
Write-Host "在 macOS/Linux 上使用前，需要添加执行权限:" -ForegroundColor Yellow
Write-Host "  chmod +x dist/augment-cleaner-linux-*" -ForegroundColor White
Write-Host "  chmod +x dist/augment-cleaner-darwin-*" -ForegroundColor White
Write-Host ""
Write-Host "📝 版本已更新至: v$newVersion" -ForegroundColor Cyan